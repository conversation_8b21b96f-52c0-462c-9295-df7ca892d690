### **Overview**

This document details the plan to enhance the `indidus_e2ee` workspace by implementing a rigorous unit testing strategy. The core problem this plan solves is the current low unit test coverage (~52.4%), which poses a significant risk of shipping buggy, insecure, or unreliable code.

This plan is for the development team. Its value lies in creating a systematic, prioritized, and actionable roadmap to increase code coverage to over 90%, thereby drastically improving code quality, reducing regressions, and building confidence in the library's security and correctness.

### **Core Features (of this Testing Plan)**

-   **Targeted Unit Testing:** Focus on modules with the lowest coverage and highest criticality, such as the Signal Protocol state machine and server-side WebSocket handlers.
-   **Dependency Mocking:** Introduce mocking for network and filesystem dependencies to enable pure, fast, and deterministic unit tests.
-   **Error Path Validation:** Ensure every possible error path in the business logic is explicitly tested.
-   **CI Quality Gates:** Systematically increase the coverage threshold required by the CI pipeline to enforce our quality standards and prevent backsliding.
-   **Advanced Testing Techniques:** Introduce property-based and mutation testing for the cryptographic core to validate its correctness beyond simple example-based tests.

<br>

---

<PRD>

### **Technical Architecture (The Unit Test Plan)**

Our primary goal is to test logic in isolation. Integration and end-to-end tests are valuable, but they don't cover the myriad of edge cases that unit tests can. We will add `#[cfg(test)]` modules to every file containing logic, ensuring tests live alongside the code they validate.

#### **1. Overall Strategy**

*   **Mocking:** We will introduce mock implementations for external boundaries.
    *   **Network:** Create a mock `WebSocketStream` trait implementation to simulate client and server network interactions without real sockets.
    *   **Filesystem:** Use libraries like `tempfile` for tests requiring disk I/O, ensuring tests are isolated and clean up after themselves.
    *   **HTTP API:** Continue using `wiremock` for `api.rs` but expand test cases to cover all documented API responses, including various HTTP error codes (400, 401, 403, 404, 500) and network-level failures.
*   **Error Path Coverage:** Every `Result::Err` and `Option::None` path must have a dedicated test case.
*   **Property-Based Testing:** For complex data structures and state machines (`indidus_signal_protocol`), we will introduce `proptest` to discover edge cases that are hard to predict.
*   **Mutation Testing:** For the `indidus_signal_protocol` crate, we will introduce `cargo-mutants` to verify the quality of our tests. High coverage is meaningless if the tests don't fail when the code is broken.

#### **2. Crate-Specific Test Plan**

Based on the latest coverage report (`cobertura.xml`), here is the prioritized plan.

##### **`indidus_signal_protocol` (Coverage: ~54%, Priority: Critical)**

This is the cryptographic core. Flaws here are catastrophic.

*   **`session/state.rs` & `session/protocol_state.rs`:**
    *   **State Transitions:** Test every single state transition in the Double Ratchet. This includes:
        *   Initiator sends the first message.
        *   Responder receives the first message and initializes their sending chain.
        *   DH ratchet step is triggered when a message with a new DH key is received.
        *   Symmetric-key ratchet step for every subsequent message.
    *   **Out-of-Order Messages:** Write explicit tests where messages are decrypted in a jumbled order. Verify that `skipped_message_keys` are populated correctly and then consumed as the old messages arrive.
    *   **Edge Cases:** Test decryption failure with corrupted ciphertext, wrong header, and messages from an unknown session.
    *   **Serialization:** Test the `to_json`/`from_json` and `to_bytes`/`from_bytes` round-trip for every possible session state (e.g., before sending, after sending, with skipped keys). Test against corrupted or malformed state data.

##### **`indidus_e2ee_server` (Coverage: ~31%, Priority: High)**

The server handles untrusted input from multiple clients. It must be robust.

*   **`handlers/websocket.rs` (Coverage: ~15%):**
    *   **Mock WebSocket:** Implement a `MockWebSocket` that allows us to feed it `ClientMessage`s and assert on the `ServerMessage`s it sends back.
    *   **Client Lifecycle:**
        *   Test successful registration.
        *   Test sending a message before registration (must fail with an error).
        *   Test registration with a `peer_id` that is already in use.
        *   Test multiple registration attempts from the same client.
        *   Test message relay between two mock clients via the `RoutingTable`.
        *   Test relaying to a non-existent/disconnected peer (sender must receive an error).
        *   Test graceful `Disconnect` message handling.
        *   Test ungraceful disconnect (mock stream ends abruptly) and ensure the `ConnectionCleanupGuard` correctly removes the peer from the `RoutingTable`.
*   **`handlers/file_transfer.rs` (Coverage: ~40%):**
    *   **Mock Multipart Requests:** Create test helpers to build `Multipart` requests programmatically.
    *   **Validation Logic:** Test all validation paths in `validate_chunk_request`: invalid transfer ID (empty, too long, path traversal), empty chunk data, and oversized chunks.
    *   **Storage Mocking:** Mock `ChunkStorageService` to test `handle_file_chunk` logic without touching the filesystem. Test how the handler reacts to storage errors (e.g., `ChunkAlreadyExists`, `Io`).
*   **`storage.rs` (Coverage: ~53%):**
    *   Use `tempfile::TempDir` to create an isolated directory for each test.
    *   Test `store_chunk` success path and verify file content.
    *   Test `store_chunk` failure when the directory is not writable (set permissions accordingly).
    *   Test `retrieve_chunk` for an existing and non-existent chunk.
    *   Test `cleanup_transfer` and verify the entire directory for that transfer is deleted.
    *   Explicitly test the path traversal validation in `validate_transfer_id`.

##### **`indidus_e2ee_client` (Coverage: ~45%, Priority: High)**

The client is the other half of the system and equally important.

*   **`connection.rs` (Coverage: ~35%):**
    *   Mock the `tokio_tungstenite::connect_async` call.
    *   Test the `event_loop_task` by sending mock `ServerMessage`s down the read half of the stream and verifying the correct `ClientEvent` is emitted.
    *   Test all `ServerMessage` variants, including malformed JSON and unsupported binary messages.
    *   Test sending `ClientMessage`s by pushing to the `message_rx` channel and asserting they are written to the mock stream's write half.
    *   Simulate connection errors and verify the state transitions to `ConnectionError`/`ConnectionLost`.
*   **`messaging.rs` & `file_transfer.rs`:**
    *   These modules contain complex logic that orchestrates crypto, state, and networking.
    *   **Isolate and Mock:** For `send_message`, mock the `ApiClient` (for X3DH path) and `message_sender` channel.
    *   **Test Paths:**
        *   `send_message` path for a new session (triggers X3DH).
        *   `send_message` path for an existing session (uses Double Ratchet).
        *   `decrypt_message` path for a pre-key message (new session).
        *   `decrypt_message` path for a standard message.
        *   `FileChunkIterator`: Test with empty files, files smaller than one chunk, and multi-chunk files.
        *   `handle_chunk_data`: Test chunk reassembly logic with in-order, out-of-order, and duplicate chunks. Test decryption failure.

### **Development Roadmap**

This project is broken down into phases based on priority and logical dependency. No timelines are attached; this is purely a scope and sequencing plan.

#### **Phase 1: Fortify the Cryptographic Core**

*   **Goal:** Achieve 95%+ coverage for the `indidus_signal_protocol` crate.
*   **Tasks:**
    1.  Write comprehensive unit tests for `session/state.rs` and `session/protocol_state.rs`, covering all Double Ratchet state transitions.
    2.  Add tests for out-of-order message handling and decryption from `skipped_message_keys`.
    3.  Add tests for all error conditions (e.g., decryption failure, invalid headers).
    4.  Introduce `cargo-mutants` to the CI pipeline for this crate to ensure tests are meaningful.
    5.  Increase CI coverage gate for this crate to 95%.

#### **Phase 2: Harden the Server**

*   **Goal:** Achieve 90%+ coverage for the `indidus_e2ee_server` crate.
*   **Tasks:**
    1.  Develop a mock `WebSocketStream` for testing handlers in isolation.
    2.  Implement the full test suite for `handlers/websocket.rs` covering the entire client lifecycle and all error paths.
    3.  Implement unit tests for `storage.rs` using `tempfile`.
    4.  Implement unit tests for `handlers/file_transfer.rs` using mock multipart requests and a mock storage service.
    5.  Increase CI coverage gate for this crate to 90%.

#### **Phase 3: Solidify the Client**

*   **Goal:** Achieve 90%+ coverage for the `indidus_e2ee_client` crate.
*   **Tasks:**
    1.  Implement the comprehensive test suite for `connection.rs` using a mocked WebSocket connection.
    2.  Write isolated unit tests for `messaging.rs` and `file_transfer.rs` by mocking their dependencies (network, crypto sessions).
    3.  Expand `api.rs` tests to cover all HTTP error codes and network failures.
    4.  Review and enhance tests for `state.rs` and `config.rs`, focusing on serialization edge cases.
    5.  Increase CI coverage gate for this crate to 90%.

#### **Phase 4: Final Polish & CI Integration**

*   **Goal:** Raise overall workspace coverage to 90%+ and lock it in.
*   **Tasks:**
    1.  Perform a final review of the coverage report for any remaining gaps.
    2.  Update the root `ci.yml` to set `--fail-under 90.0` for the entire workspace.
    3.  Document the testing strategy, including how to write effective unit tests and use the new mocking infrastructure.

### **Logical Dependency Chain**

The development order is critical to build upon a solid foundation.

1.  **Foundation (`indidus_signal_protocol`):** All other crates depend on the crypto core. Its correctness is paramount. Testing this first ensures that any issues found later are unlikely to be in the base cryptographic logic.
2.  **Shared Types (`indidus_shared`):** This is a quick win and ensures our basic shared types are sound. (Already complete).
3.  **Server Logic (`indidus_e2ee_server`):** The server is the central authority. Testing its handlers and storage ensures the backend is robust before focusing on the client that consumes it. This gets us to a "visibly working" backend quickly.
4.  **Client Logic (`indidus_e2ee_client`):** The client relies on a correct implementation of the protocol and a working server. It is tested last as it's the final piece of the puzzle.

### **Risks and Mitigations**

*   **Risk: Mocking Complexity**
    *   Mocking asynchronous traits and network components like WebSockets can be complex and time-consuming.
    *   **Mitigation:**
        1.  Define minimal, simple traits for our dependencies where possible.
        2.  Use battle-tested libraries like `tokio-test`, `wiremock`, and `axum-test-helpers` to avoid reinventing the wheel.
        3.  Keep mocks focused on behavior needed for the test, not on perfectly replicating the real component.

*   **Risk: Diminishing Returns**
    *   The final push from 85% to 90%+ coverage can involve testing trivial code (getters, simple constructors), yielding low value.
    *   **Mitigation:**
        1.  Prioritize modules based on complexity and criticality, not just raw coverage numbers.
        2.  Use the `cargo-tarpaulin` feature to ignore trivial functions.
        3.  Focus on *branch coverage* in addition to line coverage to ensure all logical paths are tested.
        4.  Implement mutation testing (`cargo-mutants`) on the crypto crate to prove that our tests are effective, not just present.

*   **Risk: Brittle Tests**
    *   Tests that are too tightly coupled to implementation details can break easily during refactoring.
    *   **Mitigation:**
        1.  Always test against the public API of a module or struct. Avoid testing private functions directly unless absolutely necessary.
        2.  Use dependency injection and mocking to decouple the unit under test from its environment.

*   **Risk: MVP Definition**
    *   The scope of work is large; defining an initial, achievable goal is key.
    *   **Mitigation:** The MVP is clearly defined in **Phase 1** of the roadmap: achieving 95%+ coverage, including mutation tests, for the `indidus_signal_protocol` crate. This delivers the highest impact on security and stability for the effort invested and provides a rock-solid foundation for subsequent phases.

### **Appendix**

*   **Baseline Coverage Report:** The current workspace coverage is **52.4%** as per the provided `cobertura.xml` file.
*   **Recommended Tooling:**
    *   Unit Testing: `tokio::test`
    *   Mocking: `wiremock`, `mockall`
    *   Property-Based Testing: `proptest`
    *   Mutation Testing: `cargo-mutants`
    *   Coverage: `cargo-tarpaulin`
*   **Testing Standard:** Every `.rs` file containing logic must have a corresponding `#[cfg(test)] mod tests { ... }` submodule. Tests should be named descriptively using the `test_functionName_whenCondition_thenExpectation` pattern.

</PRD>