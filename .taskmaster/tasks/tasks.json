{"master": {"tasks": [{"id": 1, "title": "Phase 1: Add Testing Dependencies to Cryptographic Core", "description": "Add `proptest` for property-based testing and `cargo-mutants` as a development dependency to the `indidus_signal_protocol` crate to prepare for advanced testing.", "status": "done", "dependencies": [], "priority": "high", "details": "Modify the `Cargo.toml` file for the `indidus_signal_protocol` crate. Add `proptest` to `[dependencies]` and `cargo-mutants` to `[dev-dependencies]`. Ensure versions are up-to-date.\n\n```toml\n# in indidus_signal_protocol/Cargo.toml\n\n[dependencies]\n# ... other dependencies\nproptest = \"1.4.0\"\n\n[dev-dependencies]\n# ... other dev-dependencies\ncargo-mutants = \"24.1.0\"\n```\nAfter adding the dependencies, run `cargo build -p indidus_signal_protocol` to ensure they are correctly resolved.", "testStrategy": "Verify that `cargo build` and `cargo test` for the `indidus_signal_protocol` crate complete successfully after modifying the `Cargo.toml` file.", "subtasks": [{"id": 1, "title": "Add proptest Dependency to Cargo.toml", "description": "Modify the `Cargo.toml` file for the `indidus_signal_protocol` crate to add `proptest` as a standard dependency, enabling property-based testing.", "dependencies": [], "details": "In the `[dependencies]` section of `indidus_signal_protocol/Cargo.toml`, add the line `proptest = \"1.4.0\"`. Check for the latest stable version on crates.io before finalizing.", "testStrategy": "After adding the dependency, run `cargo build -p indidus_signal_protocol` to ensure it resolves correctly. Then, execute `cargo test -p indidus_signal_protocol --all` to confirm that 100% of the existing test cases pass and no regressions were introduced.", "status": "done"}, {"id": 2, "title": "Add cargo-mutants <PERSON>den<PERSON> to Cargo.toml", "description": "Modify the `Cargo.toml` file for the `indidus_signal_protocol` crate to add `cargo-mutants` as a development dependency to support mutation testing.", "dependencies": [], "details": "In the `[dev-dependencies]` section of `indidus_signal_protocol/Cargo.toml`, add the line `cargo-mutants = \"24.1.0\"`. Verify the latest version on crates.io.", "testStrategy": "Execute `cargo build -p indidus_signal_protocol` to check if the development dependency is correctly parsed. Follow up with `cargo test -p indidus_signal_protocol --all` to ensure the crate's test suite remains fully functional and all tests pass.", "status": "done"}, {"id": 3, "title": "Verify Final Build and Test Suite Integrity", "description": "After adding both dependencies, perform a final, clean verification to ensure that the combination of `proptest` and `cargo-mutants` does not create any conflicts and the crate is in a stable state.", "dependencies": ["1", "2"], "details": "With both `proptest` and `cargo-mutants` present in `Cargo.toml`, run a clean build and execute the entire test suite to guarantee project health.", "testStrategy": "Run `cargo clean -p indidus_signal_protocol` to remove any old artifacts. Then, run `cargo test -p indidus_signal_protocol --all`. The command must complete with a success status, confirming that 100% of test cases pass and no errors occur.", "status": "done"}]}, {"id": 2, "title": "Phase 1: Test Double Ratchet State Transitions", "description": "Implement comprehensive unit tests for `session/state.rs` and `session/protocol_state.rs` covering all primary state transitions of the Double Ratchet algorithm.", "status": "done", "dependencies": [1], "priority": "high", "details": "In a `#[cfg(test)]` module, write test cases for each state transition:\n1. Initiator sends the first message.\n2. <PERSON><PERSON><PERSON><PERSON> receives the first message and initializes their sending chain.\n3. A Di<PERSON>ie-Hellman ratchet step is triggered upon receiving a message with a new DH public key.\n4. A symmetric-key ratchet step occurs for every subsequent message. \nUse fixed keys and nonces to ensure deterministic outcomes. Assert that session keys, message counters, and chain keys are updated as expected according to the Signal Protocol specification.", "testStrategy": "Create two mock session states, one for an initiator and one for a responder. Simulate a message exchange and assert that the internal state (e.g., sending chain length, receiving chain key, root key) of both parties is updated correctly after each encryption/decryption step.", "subtasks": [{"id": 1, "title": "Implement Test Setup for Session States", "description": "Create the basic testing infrastructure within a `#[cfg(test)]` module. Implement a helper function to deterministically initialize two session states, one for an initiator (<PERSON>) and one for a responder (<PERSON>), ready for a message exchange.", "dependencies": [], "details": "The helper function, e.g., `setup_test_sessions`, should use fixed, hardcoded keys (identity, pre-key, ephemeral keys) to create and return a tuple of two session state objects. This ensures that all tests running with this setup are deterministic and reproducible.", "testStrategy": "This is a foundational task. Its success will be validated by the successful execution of subsequent tests (2.2, 2.3, etc.) that depend on this setup helper.", "status": "done"}, {"id": 2, "title": "Test Initiator's State After Sending First Message", "description": "Write a unit test to verify the internal state transition of the initiator's session immediately after encrypting the very first message.", "dependencies": ["1"], "details": "Using the session setup from subtask 2.1, take the initiator's session object. Call the encryption function with a sample plaintext. After the call, assert that the session's internal state, specifically the sending chain's message counter (should be 1) and keys, has been updated correctly.", "testStrategy": "After calling the encrypt function, inspect the initiator's state object. Use `assert_eq!` to verify that the sending message number is 1 and that the sending chain key has advanced from its initial state.", "status": "done"}, {"id": 3, "title": "Test Responder's State After Receiving First Message", "description": "Write a unit test to verify the responder's state transition upon receiving and decrypting the first message from the initiator.", "dependencies": ["2"], "details": "Use the ciphertext generated from the test in subtask 2.2. Pass this ciphertext to the responder's decryption function. Assert that the original plaintext is recovered. Most importantly, assert that the responder's session state is correctly initialized: a new sending chain should be created, and the receiving chain should be properly configured based on the DH ratchet.", "testStrategy": "Call the decrypt function on the responder's session. Assert that the result is an `Ok` variant containing the correct plaintext. Inspect the responder's state to confirm that the sending chain is no longer empty and that the receiving chain key has been updated.", "status": "done"}, {"id": 4, "title": "Test Symmetric-Key Ratchet Step", "description": "Implement a test that simulates a subsequent message exchange (e.g., a second message from the initiator) to verify the state changes associated with a symmetric-key ratchet.", "dependencies": ["3"], "details": "Following the initial message exchange, have the initiator encrypt a second message for the responder. Have the responder decrypt it. Verify that the root key remains unchanged for both parties, while the sending and receiving chain keys and message counters advance as expected.", "testStrategy": "Record the root key of both sessions before this exchange. After the initiator encrypts and the responder decrypts the second message, assert that the new root keys are identical to the old ones. Use `assert_eq!` to verify the message counters (e.g., initiator's sending counter is now 2) and chain keys have advanced correctly.", "status": "done"}, {"id": 5, "title": "Test Diffie-<PERSON><PERSON>", "description": "Write a unit test that forces a <PERSON><PERSON><PERSON><PERSON><PERSON> ratchet step by simulating the receipt of a message with a new ephemeral public key, and verify the resulting state changes.", "dependencies": ["3"], "details": "Set up a scenario where one party (e.g., the responder) sends a message back to the initiator, but this message is the first in a new epoch and thus contains a new DH public key. When the initiator decrypts this message, it must perform a DH ratchet.", "testStrategy": "Record the initiator's root key before decryption. After decrypting the message containing the new DH key, assert that the initiator's root key has changed (`assert_ne!`). Also, verify that the initiator's sending and receiving chains have been completely replaced with new ones derived from the new root key.", "status": "done"}]}, {"id": 3, "title": "Phase 1: Test Out-of-Order Message Handling", "description": "Implement specific unit tests to verify the correct handling of out-of-order and delayed messages, ensuring skipped message keys are properly stored and consumed.", "status": "done", "dependencies": [2], "priority": "high", "details": "Simulate a scenario where message 2 arrives before message 1.\n1. Encrypt message 1, then message 2 from the sender.\n2. Decrypt message 2 at the receiver. Verify it succeeds and that a skipped message key for message 1 is stored in the session state.\n3. Decrypt message 1 at the receiver. Verify it succeeds using the stored key.\n4. Assert that the `skipped_message_keys` collection is now empty.\nPseudo-code:\n```rust\nlet (msg1_ciphertext, _) = sender_session.encrypt(b\"message 1\");\nlet (msg2_ciphertext, _) = sender_session.encrypt(b\"message 2\");\n\nreceiver_session.decrypt(&msg2_ciphertext).unwrap();\nassert_eq!(receiver_session.skipped_keys_count(), 1);\n\nreceiver_session.decrypt(&msg1_ciphertext).unwrap();\nassert_eq!(receiver_session.skipped_keys_count(), 0);\n```", "testStrategy": "Run the test case described in the details. Verify the internal state of the receiver's session, specifically the `skipped_message_keys` map, to ensure keys are added and removed correctly. The test passes if both messages are successfully decrypted.", "subtasks": [{"id": 1, "title": "Test Decryption of a Future Message and Storage of Skipped Key", "description": "Verify that when a receiver decrypts a message that arrives ahead of sequence, the decryption is successful and a key for the skipped message is correctly stored.", "dependencies": [], "details": "Establish a session between a sender and receiver. Encrypt two consecutive messages, `msg1` and `msg2`. Deliver only `msg2` to the receiver. The test must assert that `msg2` is successfully decrypted and that the receiver's session state now contains exactly one skipped message key, corresponding to `msg1`.", "testStrategy": "After calling `receiver_session.decrypt(&msg2_ciphertext)`, check that the result is `Ok(plaintext)`. Then, inspect the internal state of the `receiver_session` to assert that the collection of `skipped_message_keys` has a count of 1.", "status": "done"}, {"id": 2, "title": "Test Decryption of a Delayed Message Using a Stored Key", "description": "Ensure that a delayed message can be successfully decrypted using its corresponding stored key, and that the key is consumed from storage upon use.", "dependencies": ["1"], "details": "Continuing from the state in the previous subtask (where `msg2` has been processed and a key for `msg1` is stored), now deliver `msg1` to the receiver. The test must assert that `msg1` is decrypted successfully and that the `skipped_message_keys` collection is now empty.", "testStrategy": "Call `receiver_session.decrypt(&msg1_ciphertext)` and assert the correct plaintext is returned. Afterwards, assert that the count of `skipped_message_keys` in the receiver's session is 0.", "status": "done"}, {"id": 3, "title": "Test Handling of Multiple Disordered Messages", "description": "Verify the session can handle a larger gap, storing multiple skipped keys and consuming them correctly as messages arrive in a mixed-up order.", "dependencies": [], "details": "Encrypt three messages: `msg1`, `msg2`, and `msg3`. Deliver them to the receiver in a non-sequential order, for example: `msg3`, then `msg1`, then `msg2`. After each decryption, verify the plaintext is correct and the number of stored skipped keys is as expected (e.g., after `msg3`, count is 2; after `msg1`, count is 1; after `msg2`, count is 0).", "testStrategy": "For each step of the out-of-order delivery, assert both the correctness of the decrypted content using `assert_eq!` and the exact size of the `skipped_message_keys` collection.", "status": "done"}, {"id": 4, "title": "Test Rejection of Stale Message Beyond Skip Limit", "description": "Ensure that a message that is too old (i.e., arrives after the maximum number of skipped messages has been exceeded) is rejected.", "dependencies": [], "details": "Identify the protocol's maximum limit for skipped messages (e.g., `MAX_SKIPPED`). Encrypt `MAX_SKIPPED + 2` messages. Send the final message to the receiver, which will trigger the storage of `MAX_SKIPPED` keys. Then, attempt to decrypt the very first message. This decryption should fail because its key was never stored.", "testStrategy": "Call the `decrypt` function with the ciphertext of the stale message. Assert that the function returns an error, for example, by using `assert!(result.is_err())` and matching the specific error type if possible.", "status": "done"}]}, {"id": 4, "title": "Phase 1: Test Cryptographic Error Conditions", "description": "Add unit tests for all cryptographic error paths, such as decryption failures due to corrupted ciphertext, invalid headers, or messages from an unknown session.", "status": "done", "dependencies": [2], "priority": "high", "details": "Create test cases for each error scenario:\n1.  **Corrupted Ciphertext:** Take a valid ciphertext and flip a bit. Pass it to the `decrypt` function and assert that it returns a `DecryptionError` or a similar specific error type.\n2.  **Invalid Header:** Construct a message with a malformed header (e.g., incorrect public key, wrong message number). Assert that decryption fails with an appropriate error.\n3.  **Unknown Session:** Attempt to decrypt a message that was encrypted for a different session. Assert that this fails with a clear error indicating an unknown session or invalid MAC.", "testStrategy": "For each error case, call the `decrypt` method with the malformed input. Use `assert!(result.is_err())` and, if possible, match on the specific error enum variant to ensure the correct error type is returned.", "subtasks": [{"id": 1, "title": "Test Decryption Failure with Tampered Ciphertext", "description": "Create a unit test to verify that any modification to the ciphertext body causes a decryption failure, ensuring message authenticity and integrity.", "dependencies": [], "details": "First, establish a valid cryptographic session and encrypt a known plaintext to generate a legitimate ciphertext. Then, create a copy of this ciphertext and flip a single bit within the encrypted payload. Pass this corrupted ciphertext to the `decrypt` function and assert that it returns a specific error, such as `DecryptionError::InvalidMac`.", "testStrategy": "Use `assert!(result.is_err())` on the return value of the `decrypt` function when called with the corrupted input. If the error is an enum, pattern match to confirm the exact error variant is returned, verifying that the MAC check failed as expected.", "status": "done"}, {"id": 2, "title": "Test Decryption Failure with <PERSON><PERSON> Header (Invalid Public Key)", "description": "Implement a test to ensure that a message is rejected if its header contains an incorrect or unexpected <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> public key.", "dependencies": [], "details": "Generate a valid encrypted message. Before passing it to the `decrypt` function, manually reconstruct the message structure, replacing the legitimate ephemeral public key in the header with a randomly generated or otherwise invalid public key. The decryption attempt should fail.", "testStrategy": "Call the `decrypt` function with the message containing the invalid header. Assert that the result is an error, specifically one that indicates an invalid header or key mismatch, such as `DecryptionError::InvalidHeaderKey`.", "status": "done"}, {"id": 3, "title": "Test Decryption Failure with <PERSON><PERSON> Header (Invalid Message Number)", "description": "Write a unit test to confirm that decryption fails if a message header contains a message number that is out of the valid range or doesn't align with the current state.", "dependencies": [], "details": "Take a valid encrypted message (e.g., the first message, with number 0). Alter its header to an invalid message number, such as a number far in the future or a negative number if the type allows. Attempt to decrypt this modified message.", "testStrategy": "Assert that the call to `decrypt` with the manipulated message number returns an error. The error should ideally be specific to an invalid message number or a state mismatch.", "status": "done"}, {"id": 4, "title": "Test Decryption Failure for Message from an Unrelated Session", "description": "Verify that a session cannot decrypt a message that was intended for a completely different session, confirming session isolation.", "dependencies": [], "details": "Create two separate, independent sessions: Session_AB (between <PERSON> and <PERSON>) and Session_CD (between <PERSON> and <PERSON>). Encrypt a message in Session_CD. Take the resulting ciphertext and attempt to decrypt it using the state from Session_AB. The operation must fail.", "testStrategy": "Call the `decrypt` method on <PERSON>'s session state, passing in the ciphertext generated by <PERSON><PERSON> <PERSON><PERSON><PERSON> that the function returns an error, most likely a MAC validation failure, proving that the keys from the incorrect session were unable to authenticate and decrypt the message.", "status": "done"}]}, {"id": 5, "title": "Phase 1: Test Session State Serialization", "description": "Implement round-trip serialization and deserialization tests for the session state (`to_json`/`from_json`, `to_bytes`/`from_bytes`) and test against malformed input.", "status": "done", "dependencies": [2], "priority": "high", "details": "1.  **Round-trip:** Create a session in a complex state (e.g., after several messages, with skipped message keys). Serialize it to bytes/JSON, then deserialize it back into a new session object. Assert that the deserialized session is identical to the original.\n2.  **Malformed Data:** Take valid serialized data and corrupt it (e.g., truncate it, change a value). Attempt to deserialize it and assert that a `DeserializationError` is returned.", "testStrategy": "For the round-trip test, use `assert_eq!` on the original and deserialized session objects. For the error path, use `assert!(from_json(corrupted_data).is_err())` to confirm that deserialization fails gracefully.", "subtasks": [{"id": 1, "title": "Test JSON Round-Trip Serialization for Complex Session State", "description": "Verify that a session, advanced to a complex state with skipped messages, can be serialized to JSON and deserialized back into an identical object without data loss.", "dependencies": [], "details": "First, create a test helper function to establish a session and exchange several messages in a way that creates skipped message keys. Then, in a test, use this helper to get the complex session state. Serialize this state to a JSON string using `to_json`. Immediately deserialize this string back into a new session object using `from_json`.", "testStrategy": "Use `assert_eq!` to perform a deep comparison between the original session object and the one created from deserialization. The test passes if the two objects are identical in every aspect.", "status": "done"}, {"id": 2, "title": "Test Binary Round-Trip Serialization for Complex Session State", "description": "Verify that a session in a complex state can be serialized to a byte array and deserialized back into an identical object, ensuring the binary format is stable and complete.", "dependencies": [], "details": "Using the same complex session state from the previous subtask (or a recreated one), serialize the state to a byte vector using `to_bytes`. Then, deserialize this byte vector back into a new session object using `from_bytes`.", "testStrategy": "Use `assert_eq!` to compare the original session object with the deserialized object. The test passes only if the binary round-trip results in a perfect reconstruction of the original state.", "status": "done"}, {"id": 3, "title": "Test Deserialization Failure on Corrupted JSON Input", "description": "Ensure that the JSON deserialization function (`from_json`) is robust and fails gracefully when provided with malformed or incomplete data.", "dependencies": ["1"], "details": "Take a valid JSON string representing a session state. Create several test cases by corrupting this string in different ways: 1. Truncate the JSON string midway. 2. Change a critical value (e.g., a key) to an invalid format. 3. Remove a required field. For each case, attempt to deserialize the corrupted string.", "testStrategy": "For each corrupted JSON input, call the `from_json` function. Use `assert!(result.is_err())` to confirm that the function returns a `DeserializationError` instead of panicking. If possible, assert on the specific error variant.", "status": "done"}, {"id": 4, "title": "Test Deserialization Failure on Corrupted Binary Input", "description": "Ensure that the binary deserialization function (`from_bytes`) correctly rejects corrupted or incomplete byte arrays by returning an error.", "dependencies": ["2"], "details": "Generate a valid byte array from a session state. Create corrupted versions for testing: 1. A truncated byte array (e.g., removing the last few bytes). 2. A byte array where one or more bits in the middle have been flipped. Attempt to deserialize each of these corrupted inputs.", "testStrategy": "For each of the corrupted byte arrays, call the `from_bytes` function. The test passes if the function returns an `Err` variant, which should be confirmed using `assert!(result.is_err())`.", "status": "done"}]}, {"id": 6, "title": "Phase 1: Introduce Property-Based Tests for State Machine", "description": "Use `proptest` to generate random sequences of operations (encrypt, decrypt) to discover edge cases in the Double Ratchet state machine that are difficult to find with example-based tests.", "status": "pending", "dependencies": [2, 3], "priority": "high", "details": "Define a proptest `Strategy` that generates a sequence of actions, e.g., `enum Action { Encrypt(Vec<u8>), Decrypt }`. The test will maintain two session objects (sender and receiver) and a mock network (a `VecDeque` of ciphertexts).\n```rust\nuse proptest::prelude::*;\n\nproptest! {\n    #[test]\n    fn double_ratchet_never_fails(message_payloads in prop::collection::vec(prop::collection::vec(0..255u8, 1..100), 1..10)) {\n        let (mut alice, mut bob) = setup_sessions();\n        for payload in message_payloads {\n            let ciphertext = alice.encrypt(&payload);\n            let plaintext = bob.decrypt(&ciphertext).unwrap();\n            prop_assert_eq!(payload, plaintext);\n        }\n    }\n}\n```\nThis test will ensure that for any sequence of messages, what is encrypted by one party can always be decrypted by the other.", "testStrategy": "Run `cargo test`. Proptest will automatically generate and shrink failing test cases. A passing test indicates that the specified property holds for all generated inputs. The primary property to check is that `decrypt(encrypt(plaintext)) == plaintext`.", "subtasks": [{"id": 1, "title": "Define Proptest Strategy for a Bidirectional Exchange", "description": "Define the necessary `enum` and `proptest::Strategy` to generate a random sequence of actions representing a bidirectional conversation between two parties (<PERSON> and <PERSON>).", "dependencies": [], "details": "Create a public `enum Action { AliceSends(Vec<u8>), BobSends(Vec<u8>) }`. Using `proptest`'s combinators, implement a function that returns a `Strategy` which produces a `Vec<Action>`. This will form the basis for generating arbitrary test scenarios for the state machine.", "testStrategy": "This subtask is foundational. Its success will be validated by the successful compilation and execution of the property tests in the subsequent subtasks that consume this strategy."}, {"id": 2, "title": "Implement Property Test for Sequential Bidirectional Chat", "description": "Create a property test that simulates a sequential, back-and-forth conversation, ensuring that for any randomly generated sequence of actions, all messages are successfully encrypted and decrypted.", "dependencies": ["1"], "details": "Using the `Action` strategy, create a `proptest!` test case. Initialize <PERSON>'s and <PERSON>'s sessions. Iterate through the generated actions. If `AliceSends`, <PERSON> encrypts and <PERSON> decrypts. If `<PERSON>Sends`, <PERSON> encrypts and <PERSON> decrypts. The core property is `decrypt(encrypt(plaintext)) == plaintext` for every single message.", "testStrategy": "Run `cargo test`. The test passes if `proptest` is unable to find a sequence of actions that causes a panic on `unwrap()` or fails the `prop_assert_eq!` check, proving the state machine is robust for sequential conversations."}, {"id": 3, "title": "Implement Property Test Simulating Network Latency and Reordering", "description": "Write a more advanced property test that simulates an unreliable network by collecting all messages from a conversation and delivering them in a random order to test the skipped-message-key mechanism.", "dependencies": ["1"], "details": "Generate a bidirectional conversation using the `Action` strategy. As messages are encrypted, instead of immediate decryption, store them in two separate lists (one for messages to <PERSON>, one for messages to <PERSON>). After generating all ciphertexts, shuffle each list independently. Finally, attempt to decrypt all messages in the now-randomized order.", "testStrategy": "The test property is that every single decryption call must eventually succeed, regardless of the delivery order. Run `cargo test`. A passing test demonstrates that the out-of-order handling logic is sound across a vast number of permutations."}, {"id": 4, "title": "Implement Property Test for Session State Consistency", "description": "Create a property test to assert that after a full, randomly generated conversation, the session states of both parties remain consistent and are not corrupted.", "dependencies": ["2"], "details": "Extend the sequential bidirectional test. After the entire sequence of actions has been processed, perform a final check. For example, have <PERSON> send one final message to <PERSON>. The decryption must still succeed, proving the session is in a valid and usable state after many operations.", "testStrategy": "The test passes if, after a random sequence of exchanges, a final handshake message can be successfully exchanged. This ensures no edge case in the state transitions leads to a permanently broken or desynchronized session."}]}, {"id": 7, "title": "Phase 1: Integrate `cargo-mutants` into CI Pipeline", "description": "Add a new step to the CI pipeline (`ci.yml`) that runs `cargo-mutants` specifically on the `indidus_signal_protocol` crate to ensure high-quality, effective tests.", "status": "pending", "dependencies": [2, 3, 4, 5, 6], "priority": "medium", "details": "Modify the `.github/workflows/ci.yml` file. Add a new job or a step that runs only for changes in the `indidus_signal_protocol` directory.\nUse `taiki-e/install-action` for efficient installation. The command should look like: `cargo mutants -p indidus_signal_protocol -- --all-features`.\nThe job should fail if any mutants escape (i.e., if the exit code is non-zero). This enforces that tests are not just covering code, but are actually asserting correct behavior.", "testStrategy": "Create a pull request with the CI changes. To test the integration, temporarily comment out an assertion in one of the crypto tests and push the change. The `cargo-mutants` job should fail, proving the integration works. Revert the test change before merging.", "subtasks": [{"id": 1, "title": "Define a New Conditional Job in CI Workflow", "description": "Modify the `.github/workflows/ci.yml` file to add a new job, for example named `mutation_testing`, that is specifically configured to run mutation tests.", "dependencies": [], "details": "The new job should be configured to run on a standard runner (e.g., `ubuntu-latest`). Crucially, it must be set to trigger only on pull requests when files within the `indidus_signal_protocol/` path are modified, preventing unnecessary runs.", "testStrategy": "After committing the initial job structure (without the execution steps), push to a branch and open a pull request with a change inside `indidus_signal_protocol/`. Verify in the GitHub Actions tab that the new `mutation_testing` job was triggered. Then, make a change outside this directory and confirm the job is skipped."}, {"id": 2, "title": "Add Steps to Install and Execute cargo-mutants", "description": "Within the newly created CI job, add the sequence of steps required to install and run the mutation testing tool against the specified crate.", "dependencies": ["1"], "details": "Add a step that uses the `taiki-e/install-action` to efficiently install `cargo-mutants`. Follow this with a step to execute the main command: `cargo mutants -p indidus_signal_protocol -- --all-features`. The job must be configured to fail if this command returns a non-zero exit code, which happens when mutants survive.", "testStrategy": "With a codebase where all tests pass and are effective, this job should pass. The primary validation comes in the next step."}, {"id": 3, "title": "Validate the CI Integration by Intentionally Weakening a Test", "description": "To ensure the `cargo-mutants` job is effective, create a temporary change that should cause the job to fail. This proves that the integration is correctly enforcing test quality.", "dependencies": ["2"], "details": "In a test file within the `indidus_signal_protocol` crate, temporarily comment out a critical `assert!` macro. This introduces a weakness that a mutant should be able to exploit without causing a test failure. Commit this change and push it to the pull request.", "testStrategy": "Observe the CI run for the commit. The `mutation_testing` job is expected to fail with a non-zero exit code, and the logs should indicate that at least one mutant escaped. This confirms the setup is working correctly."}, {"id": 4, "title": "Revert Temporary Changes and Confirm CI Pass", "description": "After successfully verifying that the CI job fails on weakened tests, revert the temporary change and confirm that the job now passes, completing the integration.", "dependencies": ["3"], "details": "Revert the commit that commented out the assertion, restoring the test suite to its correct state. Push this final change to the pull request.", "testStrategy": "The `mutation_testing` job should now run and pass successfully, as the restored test suite is capable of killing all mutants. Once this is confirmed, the pull request is ready for review and merge."}]}, {"id": 8, "title": "Phase 1: Set 95% CI Coverage Gate for Crypto Crate", "description": "Configure `cargo-tarpaulin` in the CI pipeline to enforce a 95% unit test coverage threshold for the `indidus_signal_protocol` crate.", "status": "pending", "dependencies": [7], "priority": "medium", "details": "Update the `cargo tarpaulin` command in the `ci.yml` file. Add package selection and failure threshold flags: `cargo tarpaulin --workspace --packages indidus_signal_protocol --fail-under 95.0`. Also, configure it to ignore trivial functions using `#[cfg_attr(tarpaulin, skip)]` on simple getters/setters if necessary to achieve the target.", "testStrategy": "After implementing all Phase 1 tests, run `cargo tarpaulin --workspace --packages indidus_signal_protocol` locally to confirm coverage is >= 95%. Then, push the CI configuration change. The CI build should pass. To confirm it fails when coverage drops, temporarily remove a test and push to a test branch."}, {"id": 9, "title": "Phase 2: Develop Mock WebSocket Infrastructure", "description": "Implement a mock `WebSocketStream` using in-memory channels (`tokio::sync::mpsc`) to allow testing `handlers/websocket.rs` in complete isolation from the network.", "status": "done", "dependencies": [], "priority": "high", "details": "Instead of a full trait mock, create a test helper function that spawns the WebSocket handler and returns MPSC channels to simulate client interaction.\n\n```rust\n// in tests/websocket_helpers.rs\n\nuse tokio::sync::mpsc;\nuse indidus_e2ee_server::handlers::websocket::{handle_connection, ClientMessage, ServerMessage};\n\nstruct MockConnection {\n    server_rx: mpsc::Receiver<ServerMessage>,\n    client_tx: mpsc::Sender<ClientMessage>,\n}\n\nfn spawn_mock_connection() -> MockConnection {\n    let (client_tx, client_rx) = mpsc::channel(32);\n    let (server_tx, server_rx) = mpsc::channel(32);\n\n    // The key is to pass the channels to the handler\n    tokio::spawn(handle_connection(client_rx, server_tx, /* other state */));\n\n    MockConnection { server_rx, client_tx }\n}\n```\nThis approach directly injects the communication channels, making it simple and efficient.", "testStrategy": "Write a simple test that uses `spawn_mock_connection`, sends a `ClientMessage` down the `client_tx`, and asserts that an expected `ServerMessage` is received on the `server_rx`."}, {"id": 10, "title": "Phase 2: Test WebSocket Handler Client Lifecycle", "description": "Using the mock infrastructure, implement a full test suite for `handlers/websocket.rs` covering the entire client lifecycle and all error paths.", "status": "pending", "dependencies": [9], "priority": "high", "details": "Create test cases for each scenario:\n-   Successful registration of two clients.\n-   Message relay between the two clients.\n-   Sending a message before registration (must receive an error).\n-   Registering with a `peer_id` that is already in use.\n-   Attempting to relay a message to a non-existent peer.\n-   Handling of a graceful `Disconnect` message.\n-   Handling of an ungraceful disconnect (drop the `client_tx` channel) and verify the `ConnectionCleanupGuard` removes the peer.", "testStrategy": "For each test, set up one or more mock connections. Send a sequence of `ClientMessage`s and use `server_rx.recv().await` to assert that the correct `ServerMessage`s (or none) are sent back by the server in the correct order. Use `tokio::time::timeout` to handle cases where no message should be sent."}, {"id": 11, "title": "Phase 2: Implement Unit Tests for `storage.rs`", "description": "Write unit tests for the `ChunkStorageService` using `tempfile::TempDir` to ensure file operations are isolated and correct without affecting the actual filesystem.", "status": "done", "dependencies": [], "priority": "high", "details": "For each test function, create a `tempfile::TempDir`. Pass its path to the `ChunkStorageService` instance.\n-   `test_store_chunk`: Call `store_chunk`, then verify the file exists at the correct path within the temp dir and that its contents match the input data.\n-   `test_store_chunk_read_only_dir`: Create a temp dir, change its permissions to read-only using `std::fs::set_permissions`, and assert that `store_chunk` returns an `Io` error.\n-   `test_retrieve_chunk`: Store a chunk, then retrieve it and assert its content is correct. Test retrieval of a non-existent chunk, asserting an error is returned.\n-   `test_cleanup_transfer`: Store multiple chunks for a transfer ID, call `cleanup_transfer`, and assert the transfer-specific subdirectory is deleted.", "testStrategy": "Each test should create its own `TempDir`, perform the storage operations, and make assertions based on filesystem state (`Path::exists()`, `fs::read()`). The `TempDir` going out of scope will automatically handle cleanup."}, {"id": 12, "title": "Phase 2: Test `file_transfer.rs` Handler", "description": "Implement unit tests for `handlers/file_transfer.rs` by mocking multipart requests and the `ChunkStorageService` dependency.", "status": "pending", "dependencies": [11], "priority": "medium", "details": "1.  **Mock Storage:** Use `mockall` to create a mock `ChunkStorageService`. Define expectations on the mock (e.g., `expect_store_chunk().returning(|_| Ok(()))`).\n2.  **Mock Multipart:** Use a library like `axum-test` to build a test request with a multipart body programmatically. This avoids manual byte wrangling. [2, 11]\n3.  **Test Handler Logic:** Call the `handle_file_chunk` Axum handler using the test server/request. Test all validation paths: invalid transfer ID, empty chunk, oversized chunk. Test the success path and verify the mock storage service was called. Test storage error paths by making the mock return an error and asserting the handler returns the correct HTTP status code (e.g., 500).", "testStrategy": "Use `axum_test::TestServer`. For each validation case, build and send a corresponding multipart request. Assert that the HTTP response status code is correct (e.g., 400 for bad input, 500 for storage error, 200 for success). For the success path, use `mock.checkpoint()` to verify the mock storage was called as expected."}, {"id": 13, "title": "Phase 2: Set 90% CI Coverage Gate for Server Crate", "description": "Configure `cargo-tarpaulin` in the CI pipeline to enforce a 90% unit test coverage threshold for the `indidus_e2ee_server` crate.", "status": "pending", "dependencies": [10, 11, 12], "priority": "medium", "details": "Update the `cargo tarpaulin` command in the `ci.yml` file. Add package selection and failure threshold flags: `cargo tarpaulin --workspace --packages indidus_e2ee_server --fail-under 90.0`. This step should be added to the existing coverage job or a new job dedicated to the server crate.", "testStrategy": "Run `cargo tarpaulin --workspace --packages indidus_e2ee_server` locally to confirm coverage is >= 90%. After pushing the CI change, verify that the build passes. The gate's effectiveness can be confirmed by temporarily removing a test on a separate branch and seeing the CI job fail."}, {"id": 14, "title": "Phase 3: Test Client `connection.rs`", "description": "Implement a comprehensive test suite for `connection.rs` using a mocked WebSocket connection to simulate server interactions and test the client's event loop.", "status": "done", "dependencies": [], "priority": "high", "details": "The best way to test `connection.rs` is to mock the entire `connect_async` function and the resulting stream. A library like `ws-mock` can create a local mock WebSocket server. [12]\n1.  Start a `WSMockServer` in the test.\n2.  Configure mocks, e.g., `WsMock::new().respond_with(Message::Text(serde_json::to_string(&server_message).unwrap()))`.\n3.  Point the client's `connect` function to the mock server's URI.\n4.  Test the `event_loop_task` by asserting the correct `ClientEvent` is emitted when the mock server sends messages.\n5.  Test sending `ClientMessage`s by pushing to the client's internal channel and using the mock server to verify the message was received (`server.verify().await`).\n6.  Simulate connection errors by having the mock server abruptly close the connection.", "testStrategy": "For each test, configure the `WSMockServer` with expected messages to receive and predetermined responses to send. The test will pass if the client emits the correct events and the mock server verifies that it received the expected client messages."}, {"id": 15, "title": "Phase 3: Write Isolated Tests for `messaging.rs` and `file_transfer.rs`", "description": "Write isolated unit tests for the client-side `messaging.rs` and `file_transfer.rs` by mocking their network and crypto dependencies.", "status": "pending", "dependencies": [14, 5], "priority": "high", "details": "Use `mockall` to mock traits and channels for dependency injection.\n-   **`messaging.rs`:**\n    -   For `send_message` with a new session, mock the `ApiClient` to return a pre-key bundle. Mock the `message_sender` channel and assert a `ClientMessage::PreKeyMessage` is sent.\n    -   For an existing session, mock the `SessionStore` to return an existing session. Mock the channel and assert a `ClientMessage::StandardMessage` is sent.\n-   **`file_transfer.rs`:**\n    -   `FileChunkIterator`: Test with various file sizes (empty, <1 chunk, multiple chunks) and assert it produces the correct number of chunks with correct metadata.\n    -   `handle_chunk_data`: Simulate receiving chunks via a channel. Test in-order, out-of-order, and duplicate chunk delivery, asserting the file is correctly reassembled only when all unique chunks are present.", "testStrategy": "Each function under test should be provided with mock objects. Use `mock.expect_*()` to set up behavior and `mock.checkpoint()` or assertions on channel receivers to verify the correct interactions occurred. For file reassembly, compare the reassembled file bytes with the original file bytes."}, {"id": 16, "title": "Phase 3: Expand `api.rs` Tests for All Error Codes", "description": "Expand the existing `wiremock` tests for `api.rs` to cover all documented API responses, including various HTTP error codes (400, 401, 403, 404, 500) and network failures.", "status": "pending", "dependencies": [], "priority": "medium", "details": "For each API client function, add a test for each relevant HTTP error code.\n```rust\n// Example for a function that gets a prekey\n#[tokio::test]\nasync fn test_get_prekey_handles_404_not_found() {\n    let mock_server = MockServer::start().await;\n    Mock::given(method(\"GET\"))\n        .and(path(\"/prekey/123\"))\n        .respond_with(ResponseTemplate::new(404))\n        .mount(&mock_server)\n        .await;\n\n    let api_client = ApiClient::new(mock_server.uri());\n    let result = api_client.get_prekey(\"123\").await;\n\n    assert!(matches!(result, Err(ApiError::UserNotFound)));\n}\n```\nAlso, test network-level failures by not starting the mock server or by using a `Mock::given(...).respond_with(ResponseTemplate::new(200).with_delay(Duration::from_secs(5)))` and a short client timeout to simulate a timeout error.", "testStrategy": "Use `wiremock` to set up a mock server response for each error condition. Call the API client method and assert that the returned `Result` is an `Err` variant that correctly maps the HTTP status code to a domain-specific `ApiError` enum."}, {"id": 17, "title": "Phase 3: Set 90% CI Coverage Gate for Client Crate", "description": "Configure `cargo-tarpaulin` in the CI pipeline to enforce a 90% unit test coverage threshold for the `indidus_e2ee_client` crate.", "status": "pending", "dependencies": [14, 15, 16], "priority": "medium", "details": "Update the `cargo tarpaulin` command in the `ci.yml` file. Add package selection and failure threshold flags: `cargo tarpaulin --workspace --packages indidus_e2ee_client --fail-under 90.0`. This can be added to the same CI job as the other coverage checks.", "testStrategy": "Run `cargo tarpaulin --workspace --packages indidus_e2ee_client` locally to confirm coverage is >= 90%. After pushing the CI change, verify that the build passes. Confirm the gate's functionality by temporarily removing a test on a separate branch and ensuring the CI job fails."}, {"id": 18, "title": "Phase 4: Set Workspace-Wide 90% Coverage Gate", "description": "Update the root `ci.yml` to set a single `--fail-under 90.0` threshold for the entire workspace, ensuring a high standard of quality is maintained globally.", "status": "pending", "dependencies": [8, 13, 17], "priority": "low", "details": "Consolidate the crate-specific coverage jobs into a single step in `ci.yml`. The command should be: `cargo tarpaulin --workspace --fail-under 90.0`. Remove the individual `--packages` and `--fail-under` flags from previous steps, as this single command will now enforce the quality gate for the entire project.", "testStrategy": "Before merging, run `cargo tarpaulin --workspace` locally and verify the total coverage is above 90%. After merging the `ci.yml` change, the CI pipeline should pass. This finalizes the automated enforcement of the coverage standard."}, {"id": 19, "title": "Phase 4: Document Testing Strategy and Mocking Infrastructure", "description": "Create a new document (`TESTING.md`) in the repository that explains the unit testing strategy, how to write effective tests, and how to use the new mocking infrastructure.", "status": "pending", "dependencies": [9, 12, 14], "priority": "low", "details": "The document should include:\n1.  An overview of the testing philosophy (isolated unit tests).\n2.  Guidance on the `test_function_whenCondition_thenExpectation` naming convention.\n3.  A practical example of how to use the WebSocket mocking helper (`spawn_mock_connection`).\n4.  A practical example of mocking a service trait with `mockall` for handler testing.\n5.  Instructions on how to run coverage reports and mutation tests locally.", "testStrategy": "Submit the `TESTING.md` file in a pull request. The team will review it for clarity, correctness, and completeness. The task is complete when the documentation is merged."}], "metadata": {"created": "2025-08-18T07:53:04.747Z", "updated": "2025-08-18T11:02:11.394Z", "description": "Tasks for master context"}}}