use tokio::sync::mpsc;
use indidus_e2ee_server::handlers::websocket::{ClientMessage, ServerMessage, RoutingTable};

/// Mock WebSocket connection for testing WebSocket handlers in isolation
pub struct MockConnection {
    /// Channel to receive messages from the server
    pub server_rx: mpsc::UnboundedReceiver<ServerMessage>,
    /// Channel to send messages to the server (simulating client)
    pub client_tx: mpsc::UnboundedSender<ClientMessage>,
    /// Handle to the spawned connection task
    pub connection_handle: tokio::task::JoinHandle<()>,
}

impl MockConnection {
    /// Send a client message to the server
    pub async fn send_client_message(&self, message: ClientMessage) -> Result<(), mpsc::error::SendError<ClientMessage>> {
        self.client_tx.send(message)
    }

    /// Receive the next server message (with timeout)
    pub async fn recv_server_message(&mut self) -> Option<ServerMessage> {
        self.server_rx.recv().await
    }

    /// Receive the next server message with a timeout
    pub async fn recv_server_message_timeout(&mut self, timeout: std::time::Duration) -> Result<ServerMessage, &'static str> {
        tokio::time::timeout(timeout, self.server_rx.recv())
            .await
            .map_err(|_| "timeout")?
            .ok_or("channel closed")
    }

    /// Close the connection gracefully
    pub async fn close(self) {
        // Drop the client_tx to signal connection closure
        drop(self.client_tx);
        // Wait for the connection task to complete
        let _ = self.connection_handle.await;
    }
}

/// Spawn a mock WebSocket connection for testing
/// 
/// This function creates in-memory channels to simulate WebSocket communication
/// and spawns a task that processes messages using the actual WebSocket handler logic.
/// 
/// Returns a `MockConnection` that can be used to send client messages and receive server responses.
pub fn spawn_mock_connection() -> MockConnection {
    spawn_mock_connection_with_routing_table(RoutingTable::new())
}

/// Spawn a mock WebSocket connection with a specific routing table
/// 
/// This allows testing scenarios where multiple connections share the same routing table.
pub fn spawn_mock_connection_with_routing_table(routing_table: RoutingTable) -> MockConnection {
    // Create channels for bidirectional communication
    let (client_tx, mut client_rx) = mpsc::unbounded_channel::<ClientMessage>();
    let (server_tx, server_rx) = mpsc::unbounded_channel::<ServerMessage>();

    // Spawn a task that simulates the WebSocket handler behavior
    let connection_handle = tokio::spawn(async move {
        // Track connection state similar to the real WebSocket handler
        let mut peer_id: Option<indidus_shared::validation::PeerId> = None;
        let mut is_registered = false;

        // Process messages from the client
        while let Some(client_message) = client_rx.recv().await {
            match process_mock_client_message(
                client_message,
                &server_tx,
                &routing_table,
                &mut peer_id,
                &mut is_registered,
            ).await {
                Ok(should_continue) => {
                    if !should_continue {
                        break; // Client requested disconnection
                    }
                }
                Err(e) => {
                    // Send error message to client
                    let error_msg = ServerMessage::Error {
                        reason: e.to_string(),
                    };
                    if server_tx.send(error_msg).is_err() {
                        break; // Client disconnected
                    }
                    
                    // For authentication failures, disconnect
                    if matches!(e, indidus_e2ee_server::handlers::error::HandlerError::AuthenticationFailed(_)) {
                        break;
                    }
                }
            }
        }

        // Cleanup: remove peer from routing table if registered
        if let Some(peer_id) = peer_id {
            routing_table.remove(&peer_id).await;
        }
    });

    MockConnection {
        server_rx,
        client_tx,
        connection_handle,
    }
}

/// Process a client message in the mock environment
/// 
/// This function replicates the core logic from the WebSocket handler
/// but operates on in-memory channels instead of actual WebSocket streams.
async fn process_mock_client_message(
    message: ClientMessage,
    server_tx: &mpsc::UnboundedSender<ServerMessage>,
    routing_table: &RoutingTable,
    peer_id: &mut Option<indidus_shared::validation::PeerId>,
    is_registered: &mut bool,
) -> Result<bool, indidus_e2ee_server::handlers::error::HandlerError> {
    use indidus_e2ee_server::handlers::error::HandlerError;

    match message {
        ClientMessage::Register { peer_id: new_peer_id } => {
            // Check if already registered
            if *is_registered {
                return Err(HandlerError::InvalidRequest(
                    "Peer is already registered. Multiple registrations not allowed.".to_string(),
                ));
            }

            // Check if peer_id is already taken
            if routing_table.contains_peer(&new_peer_id).await {
                return Err(HandlerError::InvalidRequest(format!(
                    "Peer ID '{}' is already in use",
                    new_peer_id
                )));
            }

            // Store the peer in the routing table
            routing_table
                .insert(new_peer_id.clone(), server_tx.clone())
                .await;

            // Update connection state
            *peer_id = Some(new_peer_id.clone());
            *is_registered = true;

            // Send success response
            let response = ServerMessage::RegisterSuccess {
                peer_id: new_peer_id,
            };
            server_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send registration response".to_string())
            })?;

            Ok(true)
        }
        ClientMessage::Relay { recipient_id, payload } => {
            // Enforce registration requirement
            if !*is_registered {
                return Err(HandlerError::AuthenticationFailed(
                    "Must register before sending messages".to_string(),
                ));
            }

            let sender_id = peer_id.as_ref().unwrap(); // Safe because is_registered is true

            // Construct the relayed message
            let relayed_message = ServerMessage::MessageRelayed {
                sender_id: sender_id.clone(),
                payload,
            };

            // Send the message to the recipient using the routing table
            if routing_table.send_to_peer(&recipient_id, relayed_message).await {
                // Message sent successfully
            } else {
                // Recipient not found or connection failed
                routing_table.remove(&recipient_id).await;

                // Send error back to sender
                let error_response = ServerMessage::Error {
                    reason: format!(
                        "Recipient '{}' is not connected or unreachable",
                        recipient_id
                    ),
                };
                server_tx.send(error_response).map_err(|_| {
                    HandlerError::InternalError("Failed to send error response".to_string())
                })?;
            }

            Ok(true)
        }
        ClientMessage::Ping => {
            // Ping is allowed even without registration
            let response = ServerMessage::Pong;
            server_tx.send(response).map_err(|_| {
                HandlerError::InternalError("Failed to send pong response".to_string())
            })?;
            Ok(true)
        }
        ClientMessage::Disconnect => {
            Ok(false) // Signal to disconnect
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use indidus_shared::validation::PeerId;
    use std::time::Duration;

    #[tokio::test]
    async fn test_mock_connection_basic_functionality() {
        let mut mock_conn = spawn_mock_connection();

        // Test ping/pong
        mock_conn.send_client_message(ClientMessage::Ping).await.unwrap();
        let response = mock_conn.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();
        assert!(matches!(response, ServerMessage::Pong));

        mock_conn.close().await;
    }

    #[tokio::test]
    async fn test_mock_connection_registration() {
        let mut mock_conn = spawn_mock_connection();
        let peer_id = PeerId::try_from("test_peer").unwrap();

        // Test registration
        mock_conn.send_client_message(ClientMessage::Register { 
            peer_id: peer_id.clone() 
        }).await.unwrap();
        
        let response = mock_conn.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();
        assert!(matches!(response, ServerMessage::RegisterSuccess { .. }));

        mock_conn.close().await;
    }

    #[tokio::test]
    async fn test_mock_connection_message_relay() {
        let routing_table = RoutingTable::new();
        
        // Create two mock connections sharing the same routing table
        let mut conn1 = spawn_mock_connection_with_routing_table(routing_table.clone());
        let mut conn2 = spawn_mock_connection_with_routing_table(routing_table);

        let peer1_id = PeerId::try_from("peer1").unwrap();
        let peer2_id = PeerId::try_from("peer2").unwrap();

        // Register both peers
        conn1.send_client_message(ClientMessage::Register { 
            peer_id: peer1_id.clone() 
        }).await.unwrap();
        conn2.send_client_message(ClientMessage::Register { 
            peer_id: peer2_id.clone() 
        }).await.unwrap();

        // Wait for registration confirmations
        let _ = conn1.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();
        let _ = conn2.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();

        // Send message from peer1 to peer2
        let test_payload = b"Hello, peer2!".to_vec();
        conn1.send_client_message(ClientMessage::Relay {
            recipient_id: peer2_id,
            payload: test_payload.clone(),
        }).await.unwrap();

        // Peer2 should receive the relayed message
        let response = conn2.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();
        match response {
            ServerMessage::MessageRelayed { sender_id, payload } => {
                assert_eq!(sender_id, peer1_id);
                assert_eq!(payload, test_payload);
            }
            _ => panic!("Expected MessageRelayed, got {:?}", response),
        }

        conn1.close().await;
        conn2.close().await;
    }

    #[tokio::test]
    async fn test_mock_connection_unregistered_relay_fails() {
        let mut mock_conn = spawn_mock_connection();
        let peer_id = PeerId::try_from("recipient").unwrap();

        // Try to send message without registering first
        mock_conn.send_client_message(ClientMessage::Relay {
            recipient_id: peer_id,
            payload: b"test".to_vec(),
        }).await.unwrap();

        let response = mock_conn.recv_server_message_timeout(Duration::from_millis(100)).await.unwrap();
        assert!(matches!(response, ServerMessage::Error { .. }));

        mock_conn.close().await;
    }
}