use super::*;
use crate::crypto::{bundle::PreKeyBundle, keys::KeyPair};

#[test]
fn test_session_state_creation() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let session_state = state::SessionState::new(root_key, dh_key.clone(), Some(remote_key), 1000);

    assert_eq!(session_state.root_key, root_key);
    assert_eq!(session_state.get_dh_public_key(), dh_key.public_key());
    assert_eq!(session_state.remote_dh_public_key, Some(remote_key));
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);
    assert_eq!(session_state.max_skip, 1000);
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());
    assert_eq!(session_state.remote_identity_key, None);
    assert_eq!(session_state.local_identity_key, None);
    assert_eq!(session_state.created_at, None);
    assert_eq!(session_state.last_activity, None);
    assert_eq!(session_state.session_id, None);
}

#[test]
fn test_session_state_creation_with_metadata() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();
    let remote_identity = KeyPair::generate()
        .expect("Failed to generate remote identity")
        .public_key();
    let local_identity = KeyPair::generate()
        .expect("Failed to generate local identity")
        .public_key();
    let session_id = "test-session-123".to_string();

    let session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key,
        Some(remote_key),
        500,
        Some(remote_identity),
        Some(local_identity),
        Some(session_id.clone()),
    );

    assert_eq!(session_state.root_key, root_key);
    assert_eq!(session_state.max_skip, 500);
    assert_eq!(session_state.remote_identity_key, Some(remote_identity));
    assert_eq!(session_state.local_identity_key, Some(local_identity));
    assert_eq!(session_state.session_id, Some(session_id));
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_activity_update() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    // Initially no activity timestamp
    assert_eq!(session_state.last_activity, None);

    // Update activity
    session_state.update_activity();
    assert!(session_state.last_activity.is_some());

    let first_activity = session_state.last_activity.unwrap();

    // Wait a bit and update again
    std::thread::sleep(std::time::Duration::from_millis(10));
    session_state.update_activity();

    let second_activity = session_state.last_activity.unwrap();
    assert!(second_activity >= first_activity);
}

#[test]
fn test_session_state_chain_capabilities() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    // Initially can't send or receive
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Set sending chain key
    session_state.sending_chain_key = Some([1u8; 32]);
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Set receiving chain key
    session_state.receiving_chain_key = Some([2u8; 32]);
    assert!(session_state.can_send());
    assert!(session_state.can_receive());

    // Remove sending chain key
    session_state.sending_chain_key = None;
    assert!(!session_state.can_send());
    assert!(session_state.can_receive());
}

#[test]
fn test_session_state_skipped_keys_management() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let mut session_state = state::SessionState::new(root_key, dh_key, None, 2);

    // Initially no skipped keys
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());

    // Add some skipped keys
    let key1 = (vec![1, 2, 3], 1);
    let key2 = (vec![4, 5, 6], 2);

    session_state.skipped_message_keys.insert(key1, [1u8; 32]);
    assert_eq!(session_state.skipped_keys_count(), 1);
    assert!(!session_state.is_at_max_skip());

    session_state.skipped_message_keys.insert(key2, [2u8; 32]);
    assert_eq!(session_state.skipped_keys_count(), 2);
    assert!(session_state.is_at_max_skip());

    // Clear all keys
    session_state.clear_skipped_keys();
    assert_eq!(session_state.skipped_keys_count(), 0);
    assert!(!session_state.is_at_max_skip());
}

#[test]
fn test_session_state_timing_functions() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    // Session without timestamps
    let session_state = state::SessionState::new(root_key, dh_key.clone(), None, 1000);
    assert_eq!(session_state.session_age_seconds(), None);
    assert_eq!(session_state.time_since_last_activity_seconds(), None);

    // Session with timestamps
    let session_state_with_meta = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        None,
        1000,
        None,
        None,
        None,
    );

    // Should have some age (very small since just created)
    let age = session_state_with_meta.session_age_seconds();
    assert!(age.is_some());
    assert!(age.unwrap() < 10); // Should be less than 10 seconds

    let last_activity = session_state_with_meta.time_since_last_activity_seconds();
    assert!(last_activity.is_some());
    assert!(last_activity.unwrap() < 10); // Should be less than 10 seconds
}

#[test]
fn test_session_state_dh_key_access() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let session_state = state::SessionState::new(root_key, dh_key.clone(), None, 1000);

    assert_eq!(session_state.get_dh_public_key(), dh_key.public_key());
    assert_eq!(
        session_state.dh_ratchet_key.private_key(),
        dh_key.private_key()
    );
}

#[test]
fn test_session_state_with_no_remote_key() {
    let root_key = [1u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let session_state = state::SessionState::new(root_key, dh_key, None, 1000);

    assert_eq!(session_state.remote_dh_public_key, None);
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());
}

#[test]
fn test_session_state_new_initiator() {
    let x3dh_secret = [42u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    // Create a mock pre-key bundle
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64], // Mock signature
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let session_id = "test-session-initiator".to_string();
    let session_state = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000,
        Some(session_id.clone()),
    )
    .expect("Failed to create initiator session");

    // Initiator should have a sending chain but no receiving chain initially
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Should have remote DH key set
    assert_eq!(
        session_state.remote_dh_public_key,
        Some(remote_bundle.signed_prekey())
    );

    // Should have remote identity key set
    assert_eq!(
        session_state.remote_identity_key,
        Some(remote_bundle.identity_key())
    );

    // Message numbers should be initialized to 0
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);

    // Root key should be derived (different from X3DH secret)
    assert_ne!(session_state.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session_state.max_skip, 1000);

    // Should have no skipped keys initially
    assert_eq!(session_state.skipped_keys_count(), 0);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());

    // Should have session ID set
    assert_eq!(session_state.session_id, Some(session_id));
}

#[test]
fn test_session_state_new_responder() {
    let x3dh_secret = [84u8; 32];
    let our_signed_prekey = KeyPair::generate().expect("Failed to generate our signed prekey");
    let remote_identity = KeyPair::generate()
        .expect("Failed to generate remote identity")
        .public_key();
    let session_id = "test-session-responder".to_string();

    let session_state = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity,
        500,
        Some(session_id.clone()),
    )
    .expect("Failed to create responder session");

    // Responder should not be able to send or receive initially
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Should not have remote DH key set initially
    assert_eq!(session_state.remote_dh_public_key, None);

    // Should have our DH key set
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        our_signed_prekey.public_key()
    );
    assert_eq!(
        session_state.dh_ratchet_key.private_key().as_bytes(),
        our_signed_prekey.private_key().as_bytes()
    );

    // Should have remote identity key set
    assert_eq!(session_state.remote_identity_key, Some(remote_identity));

    // Message numbers should be initialized to 0
    assert_eq!(session_state.sending_message_number, 0);
    assert_eq!(session_state.receiving_message_number, 0);
    assert_eq!(session_state.previous_chain_length, 0);

    // Root key should be the X3DH secret
    assert_eq!(session_state.root_key, x3dh_secret);

    // Should have max_skip set correctly
    assert_eq!(session_state.max_skip, 500);

    // Should have no skipped keys initially
    assert_eq!(session_state.skipped_keys_count(), 0);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());

    // Should have session ID set
    assert_eq!(session_state.session_id, Some(session_id));
}

#[test]
fn test_session_state_from_shared_secret_initiator() {
    let shared_secret = [123u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");
    let remote_dh_key = KeyPair::generate()
        .expect("Failed to generate remote DH key")
        .public_key();

    let session_state = state::SessionState::from_shared_secret(
        shared_secret,
        our_dh_key,
        Some(remote_dh_key),
        1000,
        true, // is_initiator
    )
    .expect("Failed to create session from shared secret");

    // Initiator with remote DH key should be able to send
    assert!(session_state.can_send());
    assert!(!session_state.can_receive());

    // Should have remote DH key set
    assert_eq!(session_state.remote_dh_public_key, Some(remote_dh_key));

    // Root key should be derived (different from shared secret)
    assert_ne!(session_state.root_key, shared_secret);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_from_shared_secret_responder() {
    let shared_secret = [200u8; 32];
    let our_dh_key = KeyPair::generate().expect("Failed to generate our DH key");

    let session_state = state::SessionState::from_shared_secret(
        shared_secret,
        our_dh_key,
        None,
        1000,
        false, // is_initiator
    )
    .expect("Failed to create session from shared secret");

    // Responder without remote DH key should not be able to send or receive
    assert!(!session_state.can_send());
    assert!(!session_state.can_receive());

    // Should not have remote DH key set
    assert_eq!(session_state.remote_dh_public_key, None);

    // Root key should be the shared secret (no derivation for responder)
    assert_eq!(session_state.root_key, shared_secret);

    // Should have timestamps set
    assert!(session_state.created_at.is_some());
    assert!(session_state.last_activity.is_some());
}

#[test]
fn test_session_state_restore_and_activate() {
    let root_key = [99u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");

    let original_session = state::SessionState::new_with_metadata(
        root_key,
        dh_key,
        None,
        750,
        None,
        None,
        Some("restore-test".to_string()),
    );

    // Serialize the session
    let serialized = serde_json::to_string(&original_session).expect("Failed to serialize session");

    // Wait a bit to ensure timestamp difference
    std::thread::sleep(std::time::Duration::from_millis(100));

    // Restore and activate
    let restored_session =
        state::SessionState::restore_and_activate(&serialized).expect("Failed to restore session");

    // Should have same core properties
    assert_eq!(restored_session.root_key, original_session.root_key);
    assert_eq!(restored_session.max_skip, original_session.max_skip);
    assert_eq!(restored_session.session_id, original_session.session_id);

    // Activity timestamp should be updated (or at least equal due to timing precision)
    assert!(restored_session.last_activity.unwrap() >= original_session.last_activity.unwrap());
}

#[test]
fn test_session_state_initialization_deterministic() {
    let x3dh_secret = [111u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    // Same inputs should produce sessions with different DH keys (randomly generated)
    // but same other properties
    let session1 = state::SessionState::new_initiator(x3dh_secret, &remote_bundle, 1000, None)
        .expect("Failed to create session 1");
    let session2 = state::SessionState::new_initiator(x3dh_secret, &remote_bundle, 1000, None)
        .expect("Failed to create session 2");

    // DH ratchet keys should be different (randomly generated)
    assert_ne!(
        session1.dh_ratchet_key.private_key().as_bytes(),
        session2.dh_ratchet_key.private_key().as_bytes()
    );
    assert_ne!(
        session1.dh_ratchet_key.public_key().as_bytes(),
        session2.dh_ratchet_key.public_key().as_bytes()
    );

    // But other properties should be the same
    assert_eq!(session1.remote_dh_public_key, session2.remote_dh_public_key);
    assert_eq!(session1.remote_identity_key, session2.remote_identity_key);
    assert_eq!(session1.max_skip, session2.max_skip);
    assert_eq!(
        session1.sending_message_number,
        session2.sending_message_number
    );
    assert_eq!(
        session1.receiving_message_number,
        session2.receiving_message_number
    );
}

#[test]
fn test_session_state_initialization_different_secrets() {
    let x3dh_secret1 = [111u8; 32];
    let x3dh_secret2 = [222u8; 32];
    let remote_identity = KeyPair::generate().expect("Failed to generate remote identity");
    let remote_signed_prekey =
        KeyPair::generate().expect("Failed to generate remote signed prekey");

    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let session1 = state::SessionState::new_initiator(x3dh_secret1, &remote_bundle, 1000, None)
        .expect("Failed to create session 1");
    let session2 = state::SessionState::new_initiator(x3dh_secret2, &remote_bundle, 1000, None)
        .expect("Failed to create session 2");

    // Different X3DH secrets should produce different root keys and sending chain keys
    assert_ne!(session1.root_key, session2.root_key);
    assert_ne!(session1.sending_chain_key, session2.sending_chain_key);
}

#[test]
fn test_session_state_responder_initialization() {
    let x3dh_secret = [150u8; 32];
    let our_signed_prekey = KeyPair::generate().expect("Failed to generate our signed prekey");
    let remote_identity1 = KeyPair::generate()
        .expect("Failed to generate remote identity 1")
        .public_key();
    let remote_identity2 = KeyPair::generate()
        .expect("Failed to generate remote identity 2")
        .public_key();

    let session1 = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity1,
        1000,
        None,
    )
    .expect("Failed to create responder session 1");
    let session2 = state::SessionState::new_responder(
        x3dh_secret,
        our_signed_prekey.clone(),
        remote_identity2,
        1000,
        None,
    )
    .expect("Failed to create responder session 2");

    // Same X3DH secret and our key should produce same root key
    assert_eq!(session1.root_key, session2.root_key);
    assert_eq!(
        session1.dh_ratchet_key.public_key(),
        session2.dh_ratchet_key.public_key()
    );

    // But different remote identity keys
    assert_ne!(session1.remote_identity_key, session2.remote_identity_key);
    assert_eq!(session1.remote_identity_key, Some(remote_identity1));
    assert_eq!(session2.remote_identity_key, Some(remote_identity2));
}

#[test]
fn test_session_state_restore_invalid_json() {
    let invalid_json = "{ invalid json }";
    let result = state::SessionState::restore_and_activate(invalid_json);
    assert!(result.is_err());
}

#[test]
fn test_session_state_serialization_json() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),          // Use as identity key for testing
        Some(dh_key.public_key()), // Use as local identity key for testing
        Some("test-session".to_string()),
    );

    // Add some state to make serialization more interesting
    session_state.sending_chain_key = Some([1u8; 32]);
    session_state.receiving_chain_key = Some([2u8; 32]);
    session_state.sending_message_number = 10;
    session_state.receiving_message_number = 20;
    session_state.previous_chain_length = 5;

    let serialized = session_state
        .to_json()
        .expect("Failed to serialize to JSON");
    let deserialized = state::SessionState::from_json(&serialized).expect(
        "Failed to deserialize from JSON
",
    );

    assert_eq!(session_state.root_key, deserialized.root_key);
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        deserialized.dh_ratchet_key.public_key()
    );
    assert_eq!(
        session_state.remote_dh_public_key,
        deserialized.remote_dh_public_key
    );
    assert_eq!(
        session_state.sending_chain_key,
        deserialized.sending_chain_key
    );
    assert_eq!(
        session_state.receiving_chain_key,
        deserialized.receiving_chain_key
    );
    assert_eq!(
        session_state.sending_message_number,
        deserialized.sending_message_number
    );
    assert_eq!(
        session_state.receiving_message_number,
        deserialized.receiving_message_number
    );
    assert_eq!(
        session_state.previous_chain_length,
        deserialized.previous_chain_length
    );
    assert_eq!(session_state.max_skip, deserialized.max_skip);
    assert_eq!(
        session_state.remote_identity_key,
        deserialized.remote_identity_key
    );
    assert_eq!(
        session_state.local_identity_key,
        deserialized.local_identity_key
    );
    assert_eq!(session_state.session_id, deserialized.session_id);
}

#[test]
fn test_session_state_serialization_bytes() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),          // Use as identity key for testing
        Some(dh_key.public_key()), // Use as local identity key for testing
        Some("test-session-bytes".to_string()),
    );

    session_state.sending_chain_key = Some([10u8; 32]);
    session_state.receiving_chain_key = Some([20u8; 32]);

    let bytes = session_state
        .to_bytes()
        .expect("Failed to serialize to bytes");
    let deserialized =
        state::SessionState::from_bytes(&bytes).expect("Failed to deserialize from bytes");

    assert_eq!(session_state.root_key, deserialized.root_key);
    assert_eq!(
        session_state.dh_ratchet_key.public_key(),
        deserialized.dh_ratchet_key.public_key()
    );
    assert_eq!(session_state.session_id, deserialized.session_id);
}

#[test]
fn test_session_state_serialization_minimal() {
    let root_key = [42u8; 32];
    let dh_key = KeyPair::generate().expect("Failed to generate DH key");
    let remote_key = KeyPair::generate()
        .expect("Failed to generate remote key")
        .public_key();

    let mut session_state = state::SessionState::new_with_metadata(
        root_key,
        dh_key.clone(),
        Some(remote_key),
        500,
        Some(remote_key),
        Some(dh_key.public_key()),
        Some("test-session-minimal".to_string()),
    );

    session_state.sending_chain_key = Some([1u8; 32]);

    let minimal_session = session_state.to_minimal();

    // Core properties should be preserved
    assert_eq!(minimal_session.root_key, session_state.root_key);
    assert_eq!(
        minimal_session.dh_ratchet_key.public_key(),
        session_state.dh_ratchet_key.public_key()
    );
    assert_eq!(
        minimal_session.sending_chain_key,
        session_state.sending_chain_key
    );

    // Metadata should be cleared
    assert!(minimal_session.created_at.is_none());
    assert!(minimal_session.last_activity.is_none());
    assert!(minimal_session.session_id.is_none());
}

/// Test for subtask 2.2: Test Initiator's State After Sending First Message
///
/// This test verifies the internal state transition of the initiator's session
/// immediately after encrypting the very first message. It ensures that the
/// sending chain's message counter and keys are updated correctly according
/// to the Double Ratchet specification.
#[test]
fn test_initiator_state_after_sending_first_message() {
    // Setup: Create an initiator session using deterministic keys for reproducible results
    let x3dh_secret = [0x42u8; 32]; // Fixed secret for deterministic behavior
    
    // Create fixed remote keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");

    // Create a mock pre-key bundle with fixed keys
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64], // Mock signature
        onetime_prekeys: vec![],
        timestamp: None,
    };

    // Create initiator session
    let mut initiator_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-initiator-first-message".to_string()),
    )
    .expect("Failed to create initiator session");

    // Capture initial state before encryption
    let initial_sending_message_number = initiator_session.sending_message_number;
    let initial_sending_chain_key = initiator_session.sending_chain_key;
    let initial_previous_chain_length = initiator_session.previous_chain_length;
    let initial_dh_public_key = initiator_session.get_dh_public_key();

    // Verify initial state
    assert_eq!(initial_sending_message_number, 0);
    assert!(initial_sending_chain_key.is_some());
    assert_eq!(initial_previous_chain_length, 0);
    assert!(initiator_session.can_send());

    // Encrypt the first message with sample plaintext
    let sample_plaintext = b"Hello, this is the first message!";
    let encryption_result = initiator_session.encrypt(sample_plaintext);
    
    assert!(encryption_result.is_ok(), "First message encryption should succeed");
    let (header, ciphertext) = encryption_result.unwrap();

    // Verify the message header contains correct information
    assert_eq!(header.dh_public_key, initial_dh_public_key);
    assert_eq!(header.previous_chain_length, initial_previous_chain_length);
    assert_eq!(header.message_number, 0); // First message should have number 0

    // Verify ciphertext is not empty and contains nonce + encrypted data
    assert!(!ciphertext.is_empty());
    assert!(ciphertext.len() > 12); // Should have at least 12-byte nonce + some encrypted data

    // Verify state transitions after encryption
    
    // 1. Message counter should be incremented to 1
    assert_eq!(
        initiator_session.sending_message_number, 
        initial_sending_message_number + 1,
        "Sending message counter should increment from 0 to 1"
    );

    // 2. Sending chain key should be updated (derived to next key)
    assert!(initiator_session.sending_chain_key.is_some());
    assert_ne!(
        initiator_session.sending_chain_key,
        initial_sending_chain_key,
        "Sending chain key should be updated after encryption"
    );

    // 3. Previous chain length should remain unchanged for first message
    assert_eq!(
        initiator_session.previous_chain_length,
        initial_previous_chain_length,
        "Previous chain length should remain 0 for first message"
    );

    // 4. DH ratchet key should remain the same (no DH ratchet step for first message)
    assert_eq!(
        initiator_session.get_dh_public_key(),
        initial_dh_public_key,
        "DH public key should remain unchanged for first message"
    );

    // 5. Remote DH public key should remain set
    assert_eq!(
        initiator_session.remote_dh_public_key,
        Some(remote_bundle.signed_prekey()),
        "Remote DH public key should remain set"
    );

    // 6. Session should still be able to send more messages
    assert!(
        initiator_session.can_send(),
        "Session should still be able to send after first message"
    );

    // 7. Receiving capabilities should remain unchanged (still false for initiator)
    assert!(
        !initiator_session.can_receive(),
        "Initiator should not be able to receive until first message from responder"
    );

    // 8. No skipped keys should be generated during sending
    assert_eq!(
        initiator_session.skipped_keys_count(),
        0,
        "No skipped keys should exist after sending first message"
    );

    // 9. Activity timestamp should be updated
    assert!(
        initiator_session.last_activity.is_some(),
        "Last activity timestamp should be set after encryption"
    );

    // Additional verification: Encrypt a second message to ensure chain continues correctly
    let second_plaintext = b"Second message";
    let second_encryption_result = initiator_session.encrypt(second_plaintext);
    
    assert!(second_encryption_result.is_ok(), "Second message encryption should succeed");
    let (second_header, _) = second_encryption_result.unwrap();

    // Verify second message has incremented counter
    assert_eq!(
        initiator_session.sending_message_number,
        2,
        "Message counter should be 2 after second message"
    );
    assert_eq!(
        second_header.message_number,
        1,
        "Second message header should have message number 1"
    );
}

/// Test for subtask 2.3: Test Responder's State After Receiving First Message
///
/// This test verifies the responder's state transition upon receiving and decrypting
/// the first message from the initiator. It ensures that the responder's session state
/// is correctly initialized with proper sending and receiving chains after the DH ratchet.
#[test]
fn test_responder_state_after_receiving_first_message() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32]; // Same secret for both parties
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32], // Mock verifying key
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64], // Mock signature
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut initiator_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-initiator-responder-exchange".to_string()),
    )
    .expect("Failed to create initiator session");

    // Create responder session using the same signed prekey
    let mut responder_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(), // This is the responder's signed prekey
        responder_identity.public_key(), // Remote identity from responder's perspective
        1000, // max_skip
        Some("test-responder-first-message".to_string()),
    )
    .expect("Failed to create responder session");

    // Capture responder's initial state before receiving any messages
    let initial_responder_sending_chain_key = responder_session.sending_chain_key;
    let initial_responder_receiving_chain_key = responder_session.receiving_chain_key;
    let initial_responder_sending_message_number = responder_session.sending_message_number;
    let initial_responder_receiving_message_number = responder_session.receiving_message_number;
    let initial_responder_remote_dh_key = responder_session.remote_dh_public_key;

    // Verify responder's initial state
    assert!(initial_responder_sending_chain_key.is_none(), "Responder should not have sending chain initially");
    assert!(initial_responder_receiving_chain_key.is_none(), "Responder should not have receiving chain initially");
    assert_eq!(initial_responder_sending_message_number, 0);
    assert_eq!(initial_responder_receiving_message_number, 0);
    assert!(initial_responder_remote_dh_key.is_none(), "Responder should not have remote DH key initially");
    assert!(!responder_session.can_send(), "Responder should not be able to send initially");
    assert!(!responder_session.can_receive(), "Responder should not be able to receive initially");

    // Initiator encrypts the first message
    let first_message_plaintext = b"Hello from initiator to responder!";
    let encryption_result = initiator_session.encrypt(first_message_plaintext);
    assert!(encryption_result.is_ok(), "Initiator encryption should succeed");
    let (message_header, ciphertext) = encryption_result.unwrap();

    // Verify the message header from initiator
    assert_eq!(message_header.message_number, 0, "First message should have number 0");
    assert_eq!(message_header.previous_chain_length, 0, "Previous chain length should be 0");

    // Responder receives and decrypts the first message
    let decryption_result = responder_session.decrypt(&message_header, &ciphertext);
    assert!(decryption_result.is_ok(), "Responder decryption should succeed");
    let decrypted_plaintext = decryption_result.unwrap();

    // Verify the plaintext was correctly recovered
    assert_eq!(
        decrypted_plaintext,
        first_message_plaintext,
        "Decrypted plaintext should match original message"
    );

    // Verify responder's state transitions after receiving first message
    
    // 1. Responder should now have both sending and receiving chain keys
    assert!(
        responder_session.sending_chain_key.is_some(),
        "Responder should have sending chain key after DH ratchet"
    );
    assert!(
        responder_session.receiving_chain_key.is_some(),
        "Responder should have receiving chain key after DH ratchet"
    );

    // 2. Chain keys should be different from initial state (which was None)
    assert_ne!(
        responder_session.sending_chain_key,
        initial_responder_sending_chain_key,
        "Sending chain key should be established"
    );
    assert_ne!(
        responder_session.receiving_chain_key,
        initial_responder_receiving_chain_key,
        "Receiving chain key should be established"
    );

    // 3. Receiving message number should be incremented
    assert_eq!(
        responder_session.receiving_message_number,
        initial_responder_receiving_message_number + 1,
        "Receiving message number should increment from 0 to 1"
    );

    // 4. Sending message number should remain 0 (responder hasn't sent anything yet)
    assert_eq!(
        responder_session.sending_message_number,
        initial_responder_sending_message_number,
        "Sending message number should remain 0"
    );

    // 5. Remote DH key should now be set to initiator's DH key
    assert!(
        responder_session.remote_dh_public_key.is_some(),
        "Responder should have remote DH key set"
    );
    assert_eq!(
        responder_session.remote_dh_public_key.unwrap(),
        message_header.dh_public_key,
        "Remote DH key should match initiator's DH key from header"
    );

    // 6. Responder should now be able to both send and receive
    assert!(
        responder_session.can_send(),
        "Responder should be able to send after DH ratchet"
    );
    assert!(
        responder_session.can_receive(),
        "Responder should be able to receive after establishing receiving chain"
    );

    // 7. Responder's DH ratchet key should be updated (new key generated)
    assert_ne!(
        responder_session.dh_ratchet_key.public_key(),
        remote_signed_prekey.public_key(),
        "Responder's DH key should be updated after ratchet step"
    );

    // 8. No skipped keys should be generated for the first message
    assert_eq!(
        responder_session.skipped_keys_count(),
        0,
        "No skipped keys should exist after receiving first message"
    );

    // 9. Activity timestamp should be updated
    assert!(
        responder_session.last_activity.is_some(),
        "Last activity timestamp should be set after decryption"
    );

    // Additional verification: Responder should be able to send a reply
    let reply_plaintext = b"Hello back from responder!";
    let reply_encryption_result = responder_session.encrypt(reply_plaintext);
    assert!(
        reply_encryption_result.is_ok(),
        "Responder should be able to encrypt a reply message"
    );
    let (reply_header, reply_ciphertext) = reply_encryption_result.unwrap();

    // Verify reply message properties
    assert_eq!(
        reply_header.message_number,
        0,
        "Responder's first message should have number 0"
    );
    assert_eq!(
        reply_header.previous_chain_length,
        0,
        "Responder's previous chain length should be 0"
    );
    assert_ne!(
        reply_header.dh_public_key,
        message_header.dh_public_key,
        "Responder's DH key should be different from initiator's"
    );

    // Verify responder's sending counter incremented
    assert_eq!(
        responder_session.sending_message_number,
        1,
        "Responder's sending message number should be 1 after sending reply"
    );

    // Verify initiator can decrypt the reply (bidirectional communication works)
    let reply_decryption_result = initiator_session.decrypt(&reply_header, &reply_ciphertext);
    assert!(
        reply_decryption_result.is_ok(),
        "Initiator should be able to decrypt responder's reply"
    );
    let decrypted_reply = reply_decryption_result.unwrap();
    assert_eq!(
        decrypted_reply,
        reply_plaintext,
        "Initiator should correctly decrypt responder's reply"
    );
}

/// Test for subtask 2.4: Test Symmetric-Key Ratchet Step
///
/// This test verifies the symmetric-key ratchet behavior when sending subsequent
/// messages without triggering a DH ratchet step. It ensures that chain keys
/// advance properly while the root key remains unchanged.
#[test]
fn test_symmetric_key_ratchet_step() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut initiator_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000,
        Some("test-symmetric-ratchet".to_string()),
    )
    .expect("Failed to create initiator session");

    // Create responder session
    let mut responder_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000,
        Some("test-symmetric-ratchet-responder".to_string()),
    )
    .expect("Failed to create responder session");

    // Step 1: Initial message exchange to establish the session
    let first_message = b"First message to establish session";
    let (first_header, first_ciphertext) = initiator_session.encrypt(first_message)
        .expect("First message encryption should succeed");
    
    let first_decrypted = responder_session.decrypt(&first_header, &first_ciphertext)
        .expect("First message decryption should succeed");
    assert_eq!(first_decrypted, first_message);

    // Capture state after initial exchange (before symmetric ratchet)
    let initiator_root_key_before = initiator_session.root_key;
    let initiator_sending_chain_before = initiator_session.sending_chain_key;
    let initiator_sending_number_before = initiator_session.sending_message_number;
    let initiator_dh_key_before = initiator_session.get_dh_public_key();

    let responder_root_key_before = responder_session.root_key;
    let responder_receiving_chain_before = responder_session.receiving_chain_key;
    let responder_receiving_number_before = responder_session.receiving_message_number;
    let responder_dh_key_before = responder_session.get_dh_public_key();

    // Verify initial state is established
    assert!(initiator_sending_chain_before.is_some());
    assert!(responder_receiving_chain_before.is_some());
    assert_eq!(initiator_sending_number_before, 1); // After first message
    assert_eq!(responder_receiving_number_before, 1); // After receiving first message

    // Step 2: Initiator sends a second message (symmetric-key ratchet)
    let second_message = b"Second message - symmetric ratchet test";
    let (second_header, second_ciphertext) = initiator_session.encrypt(second_message)
        .expect("Second message encryption should succeed");

    // Capture initiator state after sending second message
    let initiator_root_key_after_send = initiator_session.root_key;
    let initiator_sending_chain_after_send = initiator_session.sending_chain_key;
    let initiator_sending_number_after_send = initiator_session.sending_message_number;
    let initiator_dh_key_after_send = initiator_session.get_dh_public_key();

    // Verify symmetric-key ratchet behavior for initiator (sender)
    
    // 1. Root key should remain unchanged (no DH ratchet)
    assert_eq!(
        initiator_root_key_after_send,
        initiator_root_key_before,
        "Initiator root key should remain unchanged during symmetric ratchet"
    );

    // 2. Sending chain key should advance
    assert_ne!(
        initiator_sending_chain_after_send,
        initiator_sending_chain_before,
        "Initiator sending chain key should advance"
    );

    // 3. Message counter should increment
    assert_eq!(
        initiator_sending_number_after_send,
        initiator_sending_number_before + 1,
        "Initiator sending message number should increment"
    );

    // 4. DH key should remain the same (no DH ratchet step)
    assert_eq!(
        initiator_dh_key_after_send,
        initiator_dh_key_before,
        "Initiator DH key should remain unchanged during symmetric ratchet"
    );

    // 5. Verify message header properties
    assert_eq!(
        second_header.message_number,
        1,
        "Second message should have number 1"
    );
    assert_eq!(
        second_header.dh_public_key,
        initiator_dh_key_before,
        "Message header should contain same DH key"
    );

    // Step 3: Responder receives and decrypts the second message
    let second_decrypted = responder_session.decrypt(&second_header, &second_ciphertext)
        .expect("Second message decryption should succeed");

    // Verify correct decryption
    assert_eq!(
        second_decrypted,
        second_message,
        "Second message should be correctly decrypted"
    );

    // Capture responder state after receiving second message
    let responder_root_key_after_receive = responder_session.root_key;
    let responder_receiving_chain_after_receive = responder_session.receiving_chain_key;
    let responder_receiving_number_after_receive = responder_session.receiving_message_number;
    let responder_dh_key_after_receive = responder_session.get_dh_public_key();

    // Verify symmetric-key ratchet behavior for responder (receiver)
    
    // 1. Root key should remain unchanged (no DH ratchet)
    assert_eq!(
        responder_root_key_after_receive,
        responder_root_key_before,
        "Responder root key should remain unchanged during symmetric ratchet"
    );

    // 2. Receiving chain key should advance
    assert_ne!(
        responder_receiving_chain_after_receive,
        responder_receiving_chain_before,
        "Responder receiving chain key should advance"
    );

    // 3. Receiving message counter should increment
    assert_eq!(
        responder_receiving_number_after_receive,
        responder_receiving_number_before + 1,
        "Responder receiving message number should increment"
    );

    // 4. Responder's DH key should remain the same (no DH ratchet step)
    assert_eq!(
        responder_dh_key_after_receive,
        responder_dh_key_before,
        "Responder DH key should remain unchanged during symmetric ratchet"
    );

    // Step 4: Test multiple symmetric ratchet steps
    let third_message = b"Third message - another symmetric ratchet";
    let (third_header, third_ciphertext) = initiator_session.encrypt(third_message)
        .expect("Third message encryption should succeed");

    // Verify third message properties
    assert_eq!(
        third_header.message_number,
        2,
        "Third message should have number 2"
    );
    assert_eq!(
        third_header.dh_public_key,
        initiator_dh_key_before,
        "Third message should still use same DH key"
    );

    // Capture state before third message decryption
    let responder_chain_before_third = responder_session.receiving_chain_key;
    let responder_number_before_third = responder_session.receiving_message_number;

    // Decrypt third message
    let third_decrypted = responder_session.decrypt(&third_header, &third_ciphertext)
        .expect("Third message decryption should succeed");
    assert_eq!(third_decrypted, third_message);

    // Verify continued symmetric ratcheting
    assert_ne!(
        responder_session.receiving_chain_key,
        responder_chain_before_third,
        "Receiving chain should continue to advance"
    );
    assert_eq!(
        responder_session.receiving_message_number,
        responder_number_before_third + 1,
        "Receiving message number should continue to increment"
    );

    // Step 5: Verify that root keys remain unchanged during pure symmetric ratcheting
    // (i.e., when the same party sends multiple consecutive messages)
    
    // Send a fourth message from initiator (same sender, should be pure symmetric ratchet)
    let fourth_message = b"Fourth message - pure symmetric ratchet";
    let initiator_root_before_fourth = initiator_session.root_key;
    let responder_root_before_fourth = responder_session.root_key;
    
    let (fourth_header, fourth_ciphertext) = initiator_session.encrypt(fourth_message)
        .expect("Fourth message encryption should succeed");
    
    // Verify that root keys didn't change for consecutive messages from same sender
    assert_eq!(
        initiator_session.root_key,
        initiator_root_before_fourth,
        "Initiator root key should remain unchanged for consecutive messages"
    );
    
    let fourth_decrypted = responder_session.decrypt(&fourth_header, &fourth_ciphertext)
        .expect("Fourth message decryption should succeed");
    assert_eq!(fourth_decrypted, fourth_message);
    
    assert_eq!(
        responder_session.root_key,
        responder_root_before_fourth,
        "Responder root key should remain unchanged when receiving consecutive messages"
    );
    
    // Verify message numbering for consecutive messages
    assert_eq!(
        fourth_header.message_number,
        3,
        "Fourth message should have number 3"
    );
    assert_eq!(
        initiator_session.sending_message_number,
        4,
        "Initiator should have sent 4 messages total"
    );
    assert_eq!(
        responder_session.receiving_message_number,
        4,
        "Responder should have received 4 messages total"
    );
}

/// Test for subtask 2.5: Test Diffie-Hellman Ratchet Step
///
/// This test verifies the DH ratchet behavior when receiving a message with a new
/// DH public key. It ensures that the root key is updated, new sending and receiving
/// chains are established, and the previous chain length is correctly tracked.
#[test]
fn test_diffie_hellman_ratchet_step() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut initiator_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000,
        Some("test-dh-ratchet".to_string()),
    )
    .expect("Failed to create initiator session");

    // Create responder session
    let mut responder_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000,
        Some("test-dh-ratchet-responder".to_string()),
    )
    .expect("Failed to create responder session");

    // Step 1: Initial message exchange to establish the session
    let first_message = b"First message to establish session";
    let (first_header, first_ciphertext) = initiator_session.encrypt(first_message)
        .expect("First message encryption should succeed");
    
    let first_decrypted = responder_session.decrypt(&first_header, &first_ciphertext)
        .expect("First message decryption should succeed");
    assert_eq!(first_decrypted, first_message);

    // Step 2: Send a few more messages from initiator (symmetric ratchet)
    let second_message = b"Second message from initiator";
    let (second_header, second_ciphertext) = initiator_session.encrypt(second_message)
        .expect("Second message encryption should succeed");
    
    let second_decrypted = responder_session.decrypt(&second_header, &second_ciphertext)
        .expect("Second message decryption should succeed");
    assert_eq!(second_decrypted, second_message);

    // Capture state before DH ratchet (before responder sends first message)
    let initiator_root_key_before_dh = initiator_session.root_key;
    let initiator_receiving_chain_before = initiator_session.receiving_chain_key;
    let initiator_sending_chain_before = initiator_session.sending_chain_key;
    let initiator_dh_key_before = initiator_session.get_dh_public_key();
    let initiator_remote_dh_before = initiator_session.remote_dh_public_key;
    let initiator_previous_chain_length_before = initiator_session.previous_chain_length;

    // Verify initiator state before DH ratchet
    assert!(initiator_receiving_chain_before.is_none(), "Initiator should not have receiving chain yet");
    assert!(initiator_sending_chain_before.is_some(), "Initiator should have sending chain");
    assert_eq!(initiator_session.sending_message_number, 2, "Initiator should have sent 2 messages");
    assert_eq!(initiator_session.receiving_message_number, 0, "Initiator should not have received any messages");

    // Step 3: Responder sends first message back (triggers DH ratchet on initiator)
    // This is the key step that triggers the DH ratchet
    let responder_message = b"First message from responder - triggers DH ratchet";
    let responder_dh_key_before = responder_session.get_dh_public_key();
    
    let (responder_header, responder_ciphertext) = responder_session.encrypt(responder_message)
        .expect("Responder message encryption should succeed");

    // Verify responder's message header contains a new DH key
    assert_ne!(
        responder_header.dh_public_key,
        first_header.dh_public_key,
        "Responder should have a different DH key than initiator"
    );
    assert_eq!(
        responder_header.dh_public_key,
        responder_dh_key_before,
        "Message header should contain responder's DH key"
    );
    assert_eq!(
        responder_header.message_number,
        0,
        "Responder's first message should have number 0"
    );
    assert_eq!(
        responder_header.previous_chain_length,
        0,
        "Responder's previous chain length should be 0"
    );

    // Step 4: Initiator receives responder's message (DH ratchet occurs)
    let responder_decrypted = initiator_session.decrypt(&responder_header, &responder_ciphertext)
        .expect("Initiator should decrypt responder's message");
    assert_eq!(responder_decrypted, responder_message);

    // Capture initiator state after DH ratchet
    let initiator_root_key_after_dh = initiator_session.root_key;
    let initiator_receiving_chain_after = initiator_session.receiving_chain_key;
    let initiator_sending_chain_after = initiator_session.sending_chain_key;
    let initiator_dh_key_after = initiator_session.get_dh_public_key();
    let initiator_remote_dh_after = initiator_session.remote_dh_public_key;
    let initiator_previous_chain_length_after = initiator_session.previous_chain_length;

    // Verify DH ratchet behavior for initiator (receiver of new DH key)
    
    // 1. Root key should be updated (this is the key indicator of DH ratchet)
    assert_ne!(
        initiator_root_key_after_dh,
        initiator_root_key_before_dh,
        "Initiator root key should be updated after DH ratchet"
    );

    // 2. Receiving chain should now be established
    assert!(
        initiator_receiving_chain_after.is_some(),
        "Initiator should now have receiving chain after DH ratchet"
    );
    assert_ne!(
        initiator_receiving_chain_after,
        initiator_receiving_chain_before,
        "Receiving chain should be newly established"
    );

    // 3. Sending chain should be updated (new chain derived from new root key)
    assert_ne!(
        initiator_sending_chain_after,
        initiator_sending_chain_before,
        "Initiator sending chain should be updated after DH ratchet"
    );

    // 4. DH key should be updated (new ephemeral key generated)
    assert_ne!(
        initiator_dh_key_after,
        initiator_dh_key_before,
        "Initiator should generate new DH key after DH ratchet"
    );

    // 5. Remote DH key should be updated to responder's key
    assert_eq!(
        initiator_remote_dh_after,
        Some(responder_header.dh_public_key),
        "Initiator should update remote DH key to responder's key"
    );
    assert_ne!(
        initiator_remote_dh_after,
        initiator_remote_dh_before,
        "Remote DH key should change"
    );

    // 6. Previous chain length should be reset to 0 during DH ratchet (receiver side)
    assert_eq!(
        initiator_previous_chain_length_after,
        0,
        "Previous chain length should be reset to 0 when receiving DH ratchet"
    );
    assert_eq!(
        initiator_previous_chain_length_after,
        initiator_previous_chain_length_before,
        "Previous chain length should remain 0 for receiver"
    );

    // 7. Message counters should be updated correctly
    assert_eq!(
        initiator_session.receiving_message_number,
        1,
        "Initiator should have received 1 message after DH ratchet"
    );
    assert_eq!(
        initiator_session.sending_message_number,
        0,
        "Initiator sending counter should reset to 0 after DH ratchet"
    );

    // Step 5: Verify that initiator can now send messages with new chains
    let initiator_reply = b"Reply from initiator after DH ratchet";
    let (reply_header, reply_ciphertext) = initiator_session.encrypt(initiator_reply)
        .expect("Initiator should be able to send after DH ratchet");

    // Verify reply message properties
    assert_eq!(
        reply_header.message_number,
        0,
        "Initiator's first message after DH ratchet should have number 0"
    );
    assert_eq!(
        reply_header.previous_chain_length,
        0,
        "Previous chain length should be 0 for initiator's first message after DH ratchet"
    );
    assert_eq!(
        reply_header.dh_public_key,
        initiator_dh_key_after,
        "Reply should use initiator's new DH key"
    );

    // Step 6: Responder receives initiator's reply (should trigger another DH ratchet)
    let responder_root_before_second_dh = responder_session.root_key;
    let responder_dh_before_second_dh = responder_session.get_dh_public_key();

    let reply_decrypted = responder_session.decrypt(&reply_header, &reply_ciphertext)
        .expect("Responder should decrypt initiator's reply");
    assert_eq!(reply_decrypted, initiator_reply);

    // Verify second DH ratchet occurred on responder
    assert_ne!(
        responder_session.root_key,
        responder_root_before_second_dh,
        "Responder root key should be updated after second DH ratchet"
    );
    assert_ne!(
        responder_session.get_dh_public_key(),
        responder_dh_before_second_dh,
        "Responder should generate new DH key after second DH ratchet"
    );

    // Step 7: Verify bidirectional communication continues to work
    let final_message = b"Final message to verify continued communication";
    let (final_header, final_ciphertext) = responder_session.encrypt(final_message)
        .expect("Responder should be able to send after DH ratchet");

    let final_decrypted = initiator_session.decrypt(&final_header, &final_ciphertext)
        .expect("Initiator should decrypt final message");
    assert_eq!(final_decrypted, final_message);

    // Final verification: Both parties should have synchronized state
    assert!(initiator_session.can_send(), "Initiator should be able to send");
    assert!(initiator_session.can_receive(), "Initiator should be able to receive");
    assert!(responder_session.can_send(), "Responder should be able to send");
    assert!(responder_session.can_receive(), "Responder should be able to receive");
}

/// Test for subtask 3.1: Test Decryption of a Future Message and Storage of Skipped Key
///
/// This test verifies that when a receiver decrypts a message that arrives ahead of sequence,
/// the decryption is successful and a key for the skipped message is correctly stored.
/// This is a core feature of the Double Ratchet protocol for handling out-of-order delivery.
#[test]
fn test_decryption_of_future_message_and_skipped_key_storage() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-out-of-order-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("test-out-of-order-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Verify initial state: no skipped keys
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys initially"
    );
    assert_eq!(
        receiver_session.receiving_message_number,
        1,
        "Receiver should expect message number 1 next"
    );

    // Step 2: Sender encrypts two consecutive messages (msg1 and msg2)
    let msg1_plaintext = b"Message 1 - will be delayed";
    let msg2_plaintext = b"Message 2 - will arrive first";

    let (msg1_header, _msg1_ciphertext) = sender_session.encrypt(msg1_plaintext)
        .expect("Message 1 encryption should succeed");
    
    let (msg2_header, msg2_ciphertext) = sender_session.encrypt(msg2_plaintext)
        .expect("Message 2 encryption should succeed");

    // Verify message headers have correct sequence numbers
    assert_eq!(
        msg1_header.message_number,
        1,
        "Message 1 should have sequence number 1"
    );
    assert_eq!(
        msg2_header.message_number,
        2,
        "Message 2 should have sequence number 2"
    );

    // Verify sender state after encrypting both messages
    assert_eq!(
        sender_session.sending_message_number,
        3,
        "Sender should have sent 3 messages total (initial + msg1 + msg2)"
    );

    // Step 3: Receiver gets msg2 first (out of order) - this is the key test
    let receiver_state_before_msg2 = (
        receiver_session.receiving_message_number,
        receiver_session.skipped_keys_count(),
        receiver_session.receiving_chain_key,
    );

    // Decrypt msg2 (the future message)
    let msg2_decrypted = receiver_session.decrypt(&msg2_header, &msg2_ciphertext)
        .expect("Message 2 decryption should succeed despite being out of order");

    // Verify msg2 was correctly decrypted
    assert_eq!(
        msg2_decrypted,
        msg2_plaintext,
        "Message 2 should be correctly decrypted"
    );

    // Step 4: Verify that a skipped key was stored for msg1
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Receiver should have exactly one skipped key stored (for message 1)"
    );

    // Verify that the receiving message number advanced correctly
    assert_eq!(
        receiver_session.receiving_message_number,
        3,
        "Receiver should now expect message number 3 next (after processing msg2)"
    );

    // Verify that the receiving chain key was advanced properly
    assert_ne!(
        receiver_session.receiving_chain_key,
        receiver_state_before_msg2.2,
        "Receiving chain key should be advanced after processing msg2"
    );

    // Step 5: Verify the skipped key is stored with correct metadata
    // The skipped key should be stored with the DH public key and message number
    let _expected_skip_key = (msg1_header.dh_public_key.as_bytes().to_vec(), msg1_header.message_number);
    
    // We can't directly access the skipped keys map, but we can verify by trying to decrypt msg1
    // If the key is properly stored, msg1 decryption should succeed
    
    // Step 6: Additional verification - ensure receiver can still receive new messages
    let msg3_plaintext = b"Message 3 - in order after msg2";
    let (msg3_header, msg3_ciphertext) = sender_session.encrypt(msg3_plaintext)
        .expect("Message 3 encryption should succeed");

    assert_eq!(
        msg3_header.message_number,
        3,
        "Message 3 should have sequence number 3"
    );

    let msg3_decrypted = receiver_session.decrypt(&msg3_header, &msg3_ciphertext)
        .expect("Message 3 decryption should succeed");
    
    assert_eq!(
        msg3_decrypted,
        msg3_plaintext,
        "Message 3 should be correctly decrypted"
    );

    // After receiving msg3, we should still have the skipped key for msg1
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Receiver should still have the skipped key for message 1"
    );

    assert_eq!(
        receiver_session.receiving_message_number,
        4,
        "Receiver should now expect message number 4 next"
    );

    // Step 7: Verify session health and state consistency
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages"
    );
    assert!(
        !receiver_session.is_at_max_skip(),
        "Receiver should not be at max skip limit with only 1 skipped key"
    );

    // Verify that the session is not near the skip limit
    assert!(
        !receiver_session.is_near_skip_limit(),
        "Receiver should not be near skip limit with only 1 skipped key"
    );

    // Step 8: Verify that the skipped key storage is working correctly by checking bounds
    assert!(
        receiver_session.skipped_keys_count() <= receiver_session.max_skip as usize,
        "Skipped keys count should not exceed max_skip limit"
    );

    // Final verification: The receiver should have exactly one skipped key stored
    // and should be ready to decrypt the delayed message 1 when it arrives
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Final state: receiver should have exactly one skipped key for message 1"
    );
}

/// Test for subtask 3.3: Test Handling of Multiple Disordered Messages
///
/// This test verifies that the session can handle a larger gap, storing multiple
/// skipped keys and consuming them correctly as messages arrive in a mixed-up order.
/// This tests the robustness of the out-of-order message handling system.
#[test]
fn test_handling_of_multiple_disordered_messages() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-multiple-disordered-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("test-multiple-disordered-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Verify initial state: no skipped keys, expecting message 1 next
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys initially"
    );
    assert_eq!(
        receiver_session.receiving_message_number,
        1,
        "Receiver should expect message number 1 next"
    );

    // Step 2: Sender encrypts three consecutive messages (msg1, msg2, msg3)
    let msg1_plaintext = b"Message 1 - first in sequence";
    let msg2_plaintext = b"Message 2 - second in sequence";
    let msg3_plaintext = b"Message 3 - third in sequence";

    let (msg1_header, msg1_ciphertext) = sender_session.encrypt(msg1_plaintext)
        .expect("Message 1 encryption should succeed");
    
    let (msg2_header, msg2_ciphertext) = sender_session.encrypt(msg2_plaintext)
        .expect("Message 2 encryption should succeed");
    
    let (msg3_header, msg3_ciphertext) = sender_session.encrypt(msg3_plaintext)
        .expect("Message 3 encryption should succeed");

    // Verify message headers have correct sequence numbers
    assert_eq!(msg1_header.message_number, 1, "Message 1 should have sequence number 1");
    assert_eq!(msg2_header.message_number, 2, "Message 2 should have sequence number 2");
    assert_eq!(msg3_header.message_number, 3, "Message 3 should have sequence number 3");

    // Verify sender state after encrypting all messages
    assert_eq!(
        sender_session.sending_message_number,
        4,
        "Sender should have sent 4 messages total (initial + msg1 + msg2 + msg3)"
    );

    // Step 3: Deliver messages in disordered sequence: msg3 first
    // This should create skipped keys for msg1 and msg2
    let msg3_decrypted = receiver_session.decrypt(&msg3_header, &msg3_ciphertext)
        .expect("Message 3 decryption should succeed despite being out of order");

    assert_eq!(
        msg3_decrypted,
        msg3_plaintext,
        "Message 3 should be correctly decrypted"
    );

    // After receiving msg3, we should have 2 skipped keys (for msg1 and msg2)
    assert_eq!(
        receiver_session.skipped_keys_count(),
        2,
        "Receiver should have exactly 2 skipped keys stored (for messages 1 and 2)"
    );

    // Receiver should now expect message number 4 next
    assert_eq!(
        receiver_session.receiving_message_number,
        4,
        "Receiver should now expect message number 4 next (after processing msg3)"
    );

    // Step 4: Deliver msg1 (delayed message) - should consume one skipped key
    let msg1_decrypted = receiver_session.decrypt(&msg1_header, &msg1_ciphertext)
        .expect("Message 1 decryption should succeed using stored skipped key");

    assert_eq!(
        msg1_decrypted,
        msg1_plaintext,
        "Message 1 should be correctly decrypted using skipped key"
    );

    // After receiving msg1, we should have 1 skipped key remaining (for msg2)
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Receiver should have exactly 1 skipped key remaining (for message 2)"
    );

    // Receiving message number should remain 4 (since we processed an old message)
    assert_eq!(
        receiver_session.receiving_message_number,
        4,
        "Receiver should still expect message number 4 next"
    );

    // Step 5: Deliver msg2 (last delayed message) - should consume the final skipped key
    let msg2_decrypted = receiver_session.decrypt(&msg2_header, &msg2_ciphertext)
        .expect("Message 2 decryption should succeed using stored skipped key");

    assert_eq!(
        msg2_decrypted,
        msg2_plaintext,
        "Message 2 should be correctly decrypted using skipped key"
    );

    // After receiving msg2, all skipped keys should be consumed
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys remaining (all consumed)"
    );

    // Receiving message number should still be 4
    assert_eq!(
        receiver_session.receiving_message_number,
        4,
        "Receiver should still expect message number 4 next"
    );

    // Step 6: Verify receiver can continue with normal message flow
    let msg4_plaintext = b"Message 4 - back to normal order";
    let (msg4_header, msg4_ciphertext) = sender_session.encrypt(msg4_plaintext)
        .expect("Message 4 encryption should succeed");

    assert_eq!(
        msg4_header.message_number,
        4,
        "Message 4 should have sequence number 4"
    );

    let msg4_decrypted = receiver_session.decrypt(&msg4_header, &msg4_ciphertext)
        .expect("Message 4 decryption should succeed");
    
    assert_eq!(
        msg4_decrypted,
        msg4_plaintext,
        "Message 4 should be correctly decrypted"
    );

    // After receiving msg4 in order, no skipped keys should be created
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys after receiving msg4 in order"
    );

    assert_eq!(
        receiver_session.receiving_message_number,
        5,
        "Receiver should now expect message number 5 next"
    );

    // Step 7: Test a more complex disordered scenario with additional messages
    let msg5_plaintext = b"Message 5 - will be delayed";
    let msg6_plaintext = b"Message 6 - will arrive first";
    let msg7_plaintext = b"Message 7 - will arrive second";

    let (msg5_header, msg5_ciphertext) = sender_session.encrypt(msg5_plaintext)
        .expect("Message 5 encryption should succeed");
    
    let (msg6_header, msg6_ciphertext) = sender_session.encrypt(msg6_plaintext)
        .expect("Message 6 encryption should succeed");
    
    let (msg7_header, msg7_ciphertext) = sender_session.encrypt(msg7_plaintext)
        .expect("Message 7 encryption should succeed");

    // Deliver in order: msg6, msg7, then msg5
    let msg6_decrypted = receiver_session.decrypt(&msg6_header, &msg6_ciphertext)
        .expect("Message 6 decryption should succeed");
    assert_eq!(msg6_decrypted, msg6_plaintext);
    assert_eq!(receiver_session.skipped_keys_count(), 1, "Should have 1 skipped key for msg5");

    let msg7_decrypted = receiver_session.decrypt(&msg7_header, &msg7_ciphertext)
        .expect("Message 7 decryption should succeed");
    assert_eq!(msg7_decrypted, msg7_plaintext);
    assert_eq!(receiver_session.skipped_keys_count(), 1, "Should still have 1 skipped key for msg5");

    let msg5_decrypted = receiver_session.decrypt(&msg5_header, &msg5_ciphertext)
        .expect("Message 5 decryption should succeed");
    assert_eq!(msg5_decrypted, msg5_plaintext);
    assert_eq!(receiver_session.skipped_keys_count(), 0, "All skipped keys should be consumed");

    // Step 8: Final verification of session health
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages"
    );
    assert!(
        !receiver_session.is_at_max_skip(),
        "Receiver should not be at max skip limit"
    );
    assert!(
        !receiver_session.is_near_skip_limit(),
        "Receiver should not be near skip limit"
    );

    // Verify final receiving message number
    assert_eq!(
        receiver_session.receiving_message_number,
        8,
        "Receiver should expect message number 8 next"
    );
}

/// Test for subtask 3.4: Test Rejection of Stale Message Beyond Skip Limit
///
/// This test ensures that a message that is too old (i.e., arrives after the maximum
/// number of skipped messages has been exceeded) is rejected. This is a critical
/// security feature to prevent unbounded memory growth and potential DoS attacks.
#[test]
fn test_rejection_of_stale_message_beyond_skip_limit() {
    // Setup: Create sessions with a small max_skip limit for easier testing
    let x3dh_secret = [0x42u8; 32];
    let max_skip = 5u32; // Small limit for testing
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        max_skip,
        Some("test-skip-limit-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        max_skip,
        Some("test-skip-limit-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Verify initial state
    assert_eq!(receiver_session.max_skip, max_skip, "Max skip should be set correctly");
    assert_eq!(receiver_session.skipped_keys_count(), 0, "No skipped keys initially");
    assert_eq!(receiver_session.receiving_message_number, 1, "Expecting message 1 next");

    // Step 2: Encrypt max_skip + 2 messages (7 total: msg1 through msg7)
    // This will test the boundary condition
    let mut encrypted_messages = Vec::new();
    
    for i in 1..=(max_skip + 2) {
        let plaintext = format!("Message {} - testing skip limit", i);
        let (header, ciphertext) = sender_session.encrypt(plaintext.as_bytes())
            .expect(&format!("Message {} encryption should succeed", i));
        encrypted_messages.push((header, ciphertext, plaintext));
    }

    // Verify we have the expected number of messages
    assert_eq!(
        encrypted_messages.len(),
        (max_skip + 2) as usize,
        "Should have encrypted max_skip + 2 messages"
    );

    // Verify message sequence numbers
    for (i, (header, _, _)) in encrypted_messages.iter().enumerate() {
        assert_eq!(
            header.message_number,
            (i + 1) as u32,
            "Message {} should have correct sequence number",
            i + 1
        );
    }

    // Step 3: Send the final message (msg7) to the receiver
    // This should cause max_skip (5) keys to be stored for messages 1-5
    // Message 6 will also be skipped, but this should exceed the limit
    let final_message_index = encrypted_messages.len() - 1;
    let (final_header, final_ciphertext, _final_plaintext) = &encrypted_messages[final_message_index];

    // Before decrypting the final message, verify we're about to exceed the limit
    let expected_skipped_count = final_header.message_number - receiver_session.receiving_message_number;
    assert!(
        expected_skipped_count > max_skip,
        "Final message should require skipping more than max_skip messages"
    );

    // Attempt to decrypt the final message - this should fail due to skip limit
    let final_decrypt_result = receiver_session.decrypt(final_header, final_ciphertext);
    
    assert!(
        final_decrypt_result.is_err(),
        "Decryption should fail when skip limit is exceeded"
    );

    // Verify the error is specifically about message ordering/skip limit
    match final_decrypt_result.unwrap_err() {
        crate::error::ProtocolError::MessageOutOfOrder { received, expected } => {
            assert_eq!(received, final_header.message_number);
            assert_eq!(expected, receiver_session.receiving_message_number);
        }
        other_error => {
            panic!("Expected MessageOutOfOrder error, got: {:?}", other_error);
        }
    }

    // Step 4: Verify receiver state is unchanged after failed decryption
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "No skipped keys should be stored after failed decryption"
    );
    assert_eq!(
        receiver_session.receiving_message_number,
        1,
        "Receiving message number should be unchanged after failed decryption"
    );

    // Step 5: Test the boundary condition - decrypt a message that's exactly at the limit
    // Create a new scenario with exactly max_skip skipped messages
    let mut boundary_sender = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        max_skip,
        Some("test-boundary-sender".to_string()),
    )
    .expect("Failed to create boundary sender session");

    let mut boundary_receiver = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        max_skip,
        Some("test-boundary-receiver".to_string()),
    )
    .expect("Failed to create boundary receiver session");

    // Establish session
    let (boundary_initial_header, boundary_initial_ciphertext) = boundary_sender.encrypt(b"boundary initial")
        .expect("Boundary initial encryption should succeed");
    boundary_receiver.decrypt(&boundary_initial_header, &boundary_initial_ciphertext)
        .expect("Boundary initial decryption should succeed");

    // Encrypt max_skip + 1 messages
    let mut boundary_messages = Vec::new();
    for i in 1..=(max_skip + 1) {
        let plaintext = format!("Boundary message {}", i);
        let (header, ciphertext) = boundary_sender.encrypt(plaintext.as_bytes())
            .expect(&format!("Boundary message {} encryption should succeed", i));
        boundary_messages.push((header, ciphertext, plaintext));
    }

    // Send the message that's exactly at the limit (max_skip messages will be skipped)
    let boundary_final_index = boundary_messages.len() - 1;
    let (boundary_final_header, boundary_final_ciphertext, boundary_final_plaintext) = 
        &boundary_messages[boundary_final_index];

    let boundary_skipped_count = boundary_final_header.message_number - boundary_receiver.receiving_message_number;
    assert_eq!(
        boundary_skipped_count, max_skip,
        "Should skip exactly max_skip messages"
    );

    // This should succeed (exactly at the limit)
    let boundary_decrypt_result = boundary_receiver.decrypt(boundary_final_header, boundary_final_ciphertext);
    assert!(
        boundary_decrypt_result.is_ok(),
        "Decryption should succeed when exactly at skip limit"
    );

    let boundary_decrypted = boundary_decrypt_result.unwrap();
    assert_eq!(
        boundary_decrypted,
        boundary_final_plaintext.as_bytes(),
        "Boundary message should be correctly decrypted"
    );

    // Verify skipped keys were stored
    assert_eq!(
        boundary_receiver.skipped_keys_count(),
        max_skip as usize,
        "Should have exactly max_skip skipped keys stored"
    );

    // Step 6: Test that we can still decrypt the skipped messages within the limit
    for i in 0..(max_skip as usize) {
        let (header, ciphertext, expected_plaintext) = &boundary_messages[i];
        let decrypted = boundary_receiver.decrypt(header, ciphertext)
            .expect(&format!("Should be able to decrypt skipped message {}", i + 1));
        assert_eq!(
            decrypted,
            expected_plaintext.as_bytes(),
            "Skipped message {} should be correctly decrypted",
            i + 1
        );
    }

    // All skipped keys should now be consumed
    assert_eq!(
        boundary_receiver.skipped_keys_count(),
        0,
        "All skipped keys should be consumed after decrypting all messages"
    );

    // Step 7: Verify session health after boundary testing
    assert!(
        boundary_receiver.can_receive(),
        "Receiver should still be able to receive messages"
    );
    assert!(
        boundary_receiver.can_send(),
        "Receiver should still be able to send messages"
    );
    assert!(
        !boundary_receiver.is_at_max_skip(),
        "Receiver should not be at max skip limit after consuming all keys"
    );
}

/// Test for subtask 3.2: Test Decryption of a Delayed Message Using a Stored Key
///
/// This test ensures that a delayed message can be successfully decrypted using its
/// corresponding stored key, and that the key is consumed from storage upon use.
/// This completes the out-of-order message handling cycle.
#[test]
fn test_decryption_of_delayed_message_using_stored_key() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-delayed-message-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("test-delayed-message-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Verify initial state: no skipped keys, expecting message 1 next
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys initially"
    );
    assert_eq!(
        receiver_session.receiving_message_number,
        1,
        "Receiver should expect message number 1 next"
    );

    // Step 2: Sender encrypts two consecutive messages (msg1 and msg2)
    let msg1_plaintext = b"Message 1 - will be delayed and arrive second";
    let msg2_plaintext = b"Message 2 - will arrive first";

    let (msg1_header, msg1_ciphertext) = sender_session.encrypt(msg1_plaintext)
        .expect("Message 1 encryption should succeed");
    
    let (msg2_header, msg2_ciphertext) = sender_session.encrypt(msg2_plaintext)
        .expect("Message 2 encryption should succeed");

    // Verify message headers have correct sequence numbers
    assert_eq!(
        msg1_header.message_number,
        1,
        "Message 1 should have sequence number 1"
    );
    assert_eq!(
        msg2_header.message_number,
        2,
        "Message 2 should have sequence number 2"
    );

    // Step 3: Receiver gets msg2 first (out of order) - recreating the scenario from subtask 3.1
    let receiver_state_before_msg2 = (
        receiver_session.receiving_message_number,
        receiver_session.skipped_keys_count(),
        receiver_session.receiving_chain_key,
    );

    // Decrypt msg2 (the future message)
    let msg2_decrypted = receiver_session.decrypt(&msg2_header, &msg2_ciphertext)
        .expect("Message 2 decryption should succeed despite being out of order");

    // Verify msg2 was correctly decrypted
    assert_eq!(
        msg2_decrypted,
        msg2_plaintext,
        "Message 2 should be correctly decrypted"
    );

    // Step 4: Verify that a skipped key was stored for msg1 (state from subtask 3.1)
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Receiver should have exactly one skipped key stored (for message 1)"
    );

    // Verify that the receiving message number advanced correctly
    assert_eq!(
        receiver_session.receiving_message_number,
        3,
        "Receiver should now expect message number 3 next (after processing msg2)"
    );

    // Verify that the receiving chain key was advanced properly
    assert_ne!(
        receiver_session.receiving_chain_key,
        receiver_state_before_msg2.2,
        "Receiving chain key should be advanced after processing msg2"
    );

    // Step 5: Now deliver the delayed msg1 - this is the core test for subtask 3.2
    let receiver_state_before_msg1 = (
        receiver_session.receiving_message_number,
        receiver_session.skipped_keys_count(),
        receiver_session.receiving_chain_key,
    );

    // Decrypt msg1 (the delayed message) using the stored skipped key
    let msg1_decrypted = receiver_session.decrypt(&msg1_header, &msg1_ciphertext)
        .expect("Message 1 decryption should succeed using stored skipped key");

    // Verify msg1 was correctly decrypted
    assert_eq!(
        msg1_decrypted,
        msg1_plaintext,
        "Message 1 should be correctly decrypted using the stored key"
    );

    // Step 6: Verify that the skipped key was consumed from storage
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Skipped keys collection should now be empty (key consumed)"
    );

    // Verify that the receiving message number remains unchanged (old message processed)
    assert_eq!(
        receiver_session.receiving_message_number,
        receiver_state_before_msg1.0,
        "Receiving message number should remain unchanged when processing old message"
    );

    // Verify that the receiving chain key remains unchanged (old message doesn't advance chain)
    assert_eq!(
        receiver_session.receiving_chain_key,
        receiver_state_before_msg1.2,
        "Receiving chain key should remain unchanged when processing old message"
    );

    // Step 7: Verify that normal message flow can continue after consuming the skipped key
    let msg3_plaintext = b"Message 3 - normal flow after delayed message";
    let (msg3_header, msg3_ciphertext) = sender_session.encrypt(msg3_plaintext)
        .expect("Message 3 encryption should succeed");

    assert_eq!(
        msg3_header.message_number,
        3,
        "Message 3 should have sequence number 3"
    );

    let msg3_decrypted = receiver_session.decrypt(&msg3_header, &msg3_ciphertext)
        .expect("Message 3 decryption should succeed");
    
    assert_eq!(
        msg3_decrypted,
        msg3_plaintext,
        "Message 3 should be correctly decrypted"
    );

    // After receiving msg3 in order, no skipped keys should be created
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "Receiver should have no skipped keys after receiving msg3 in order"
    );

    assert_eq!(
        receiver_session.receiving_message_number,
        4,
        "Receiver should now expect message number 4 next"
    );

    // Step 8: Test multiple delayed messages scenario
    let msg4_plaintext = b"Message 4 - will be delayed";
    let msg5_plaintext = b"Message 5 - will also be delayed";
    let msg6_plaintext = b"Message 6 - will arrive first";

    let (msg4_header, msg4_ciphertext) = sender_session.encrypt(msg4_plaintext)
        .expect("Message 4 encryption should succeed");
    
    let (msg5_header, msg5_ciphertext) = sender_session.encrypt(msg5_plaintext)
        .expect("Message 5 encryption should succeed");
    
    let (msg6_header, msg6_ciphertext) = sender_session.encrypt(msg6_plaintext)
        .expect("Message 6 encryption should succeed");

    // Deliver msg6 first (creates 2 skipped keys for msg4 and msg5)
    let msg6_decrypted = receiver_session.decrypt(&msg6_header, &msg6_ciphertext)
        .expect("Message 6 decryption should succeed");
    assert_eq!(msg6_decrypted, msg6_plaintext);
    assert_eq!(
        receiver_session.skipped_keys_count(),
        2,
        "Should have 2 skipped keys for msg4 and msg5"
    );

    // Deliver msg4 (delayed) - should consume one skipped key
    let msg4_decrypted = receiver_session.decrypt(&msg4_header, &msg4_ciphertext)
        .expect("Message 4 decryption should succeed using stored key");
    assert_eq!(msg4_decrypted, msg4_plaintext);
    assert_eq!(
        receiver_session.skipped_keys_count(),
        1,
        "Should have 1 skipped key remaining for msg5"
    );

    // Deliver msg5 (delayed) - should consume the last skipped key
    let msg5_decrypted = receiver_session.decrypt(&msg5_header, &msg5_ciphertext)
        .expect("Message 5 decryption should succeed using stored key");
    assert_eq!(msg5_decrypted, msg5_plaintext);
    assert_eq!(
        receiver_session.skipped_keys_count(),
        0,
        "All skipped keys should be consumed"
    );

    // Step 9: Verify session health after processing delayed messages
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages"
    );
    assert!(
        !receiver_session.is_at_max_skip(),
        "Receiver should not be at max skip limit"
    );
    assert!(
        !receiver_session.is_near_skip_limit(),
        "Receiver should not be near skip limit"
    );

    // Step 10: Final verification - ensure the complete out-of-order cycle works
    // This demonstrates the complete scenario described in the parent task
    let final_msg1_plaintext = b"Final test message 1";
    let final_msg2_plaintext = b"Final test message 2";

    let (final_msg1_header, final_msg1_ciphertext) = sender_session.encrypt(final_msg1_plaintext)
        .expect("Final message 1 encryption should succeed");
    
    let (final_msg2_header, final_msg2_ciphertext) = sender_session.encrypt(final_msg2_plaintext)
        .expect("Final message 2 encryption should succeed");

    // Deliver msg2 first
    let final_msg2_decrypted = receiver_session.decrypt(&final_msg2_header, &final_msg2_ciphertext)
        .expect("Final message 2 decryption should succeed");
    assert_eq!(final_msg2_decrypted, final_msg2_plaintext);
    assert_eq!(receiver_session.skipped_keys_count(), 1, "Should have 1 skipped key");

    // Deliver msg1 second (delayed)
    let final_msg1_decrypted = receiver_session.decrypt(&final_msg1_header, &final_msg1_ciphertext)
        .expect("Final message 1 decryption should succeed using stored key");
    assert_eq!(final_msg1_decrypted, final_msg1_plaintext);
    assert_eq!(receiver_session.skipped_keys_count(), 0, "Skipped keys should be empty");

    // This completes the exact scenario described in the parent task implementation details
}

/// Test for subtask 4.1: Test Decryption Failure with Tampered Ciphertext
///
/// This test verifies that any modification to the ciphertext body causes a decryption failure,
/// ensuring message authenticity and integrity. This is a critical security feature that
/// protects against tampering and corruption.
#[test]
fn test_decryption_failure_with_tampered_ciphertext() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-tampered-ciphertext-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("test-tampered-ciphertext-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message to set up cryptographic state
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Step 2: Create a legitimate message with known plaintext
    let test_plaintext = b"This is a test message that will be tampered with";
    let (message_header, legitimate_ciphertext) = sender_session.encrypt(test_plaintext)
        .expect("Test message encryption should succeed");

    // Verify the legitimate message can be decrypted successfully first
    let mut receiver_session_copy = receiver_session.clone();
    let legitimate_decrypted = receiver_session_copy.decrypt(&message_header, &legitimate_ciphertext)
        .expect("Legitimate message decryption should succeed");
    assert_eq!(
        legitimate_decrypted,
        test_plaintext,
        "Legitimate message should decrypt correctly"
    );

    // Step 3: Test tampering with different parts of the ciphertext
    // Use fresh receiver sessions for each test to avoid state corruption
    
    // Test 3a: Flip a bit in the encrypted payload (after the nonce)
    let mut tampered_ciphertext_payload = legitimate_ciphertext.clone();
    if tampered_ciphertext_payload.len() > 12 {
        // Flip a bit in the encrypted data (not the nonce)
        tampered_ciphertext_payload[15] ^= 0x01; // Flip the least significant bit
        
        let mut fresh_receiver = receiver_session.clone();
        let tamper_result = fresh_receiver.decrypt(&message_header, &tampered_ciphertext_payload);
        assert!(
            tamper_result.is_err(),
            "Decryption should fail when ciphertext payload is tampered"
        );
        
        // Verify the error is a decryption failure
        match tamper_result.unwrap_err() {
            crate::error::ProtocolError::DecryptionFailure { reason } => {
                assert!(
                    reason.contains("Failed to decrypt message"),
                    "Error should indicate decryption failure, got: {}",
                    reason
                );
            }
            other_error => {
                panic!("Expected DecryptionFailure error, got: {:?}", other_error);
            }
        }
    }

    // Test 3b: Flip a bit in the nonce (first 12 bytes)
    let mut tampered_ciphertext_nonce = legitimate_ciphertext.clone();
    if tampered_ciphertext_nonce.len() > 12 {
        // Flip a bit in the nonce
        tampered_ciphertext_nonce[5] ^= 0x80; // Flip the most significant bit of byte 5
        
        let mut fresh_receiver = receiver_session.clone();
        let tamper_result = fresh_receiver.decrypt(&message_header, &tampered_ciphertext_nonce);
        assert!(
            tamper_result.is_err(),
            "Decryption should fail when nonce is tampered"
        );
        
        // Verify the error is a decryption failure
        match tamper_result.unwrap_err() {
            crate::error::ProtocolError::DecryptionFailure { reason } => {
                assert!(
                    reason.contains("Failed to decrypt message"),
                    "Error should indicate decryption failure, got: {}",
                    reason
                );
            }
            other_error => {
                panic!("Expected DecryptionFailure error, got: {:?}", other_error);
            }
        }
    }

    // Test 3c: Truncate the ciphertext
    let mut truncated_ciphertext = legitimate_ciphertext.clone();
    if truncated_ciphertext.len() > 5 {
        truncated_ciphertext.truncate(truncated_ciphertext.len() - 5);
        
        let mut fresh_receiver = receiver_session.clone();
        let truncate_result = fresh_receiver.decrypt(&message_header, &truncated_ciphertext);
        assert!(
            truncate_result.is_err(),
            "Decryption should fail when ciphertext is truncated"
        );
        
        // Verify the error is a decryption failure
        match truncate_result.unwrap_err() {
            crate::error::ProtocolError::DecryptionFailure { reason } => {
                assert!(
                    reason.contains("Failed to decrypt message"),
                    "Error should indicate decryption failure, got: {}",
                    reason
                );
            }
            other_error => {
                panic!("Expected DecryptionFailure error, got: {:?}", other_error);
            }
        }
    }

    // Test 3d: Append extra bytes to the ciphertext
    let mut extended_ciphertext = legitimate_ciphertext.clone();
    extended_ciphertext.extend_from_slice(&[0xFF, 0xEE, 0xDD, 0xCC]);
    
    let mut fresh_receiver = receiver_session.clone();
    let extend_result = fresh_receiver.decrypt(&message_header, &extended_ciphertext);
    assert!(
        extend_result.is_err(),
        "Decryption should fail when ciphertext has extra bytes"
    );
    
    // Verify the error is a decryption failure
    match extend_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure error, got: {:?}", other_error);
        }
    }

    // Test 3e: Replace entire ciphertext with random data
    let random_ciphertext = vec![0x42u8; legitimate_ciphertext.len()];
    
    let mut fresh_receiver = receiver_session.clone();
    let random_result = fresh_receiver.decrypt(&message_header, &random_ciphertext);
    assert!(
        random_result.is_err(),
        "Decryption should fail when ciphertext is completely replaced"
    );
    
    // Verify the error is a decryption failure
    match random_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure error, got: {:?}", other_error);
        }
    }

    // Step 4: Verify that the receiver session state is not corrupted by failed decryption attempts
    // The session should still be able to decrypt legitimate messages
    let second_test_plaintext = b"Second test message after tampering attempts";
    let (second_header, second_ciphertext) = sender_session.encrypt(second_test_plaintext)
        .expect("Second message encryption should succeed");

    let second_decrypted = receiver_session.decrypt(&second_header, &second_ciphertext)
        .expect("Second message decryption should succeed after failed tamper attempts");
    assert_eq!(
        second_decrypted,
        second_test_plaintext,
        "Session should still work normally after failed decryption attempts"
    );

    // Step 5: Verify session health after tampering tests
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages after tampering tests"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages after tampering tests"
    );

    // Step 6: Test edge case - empty ciphertext
    let empty_ciphertext = vec![];
    let mut fresh_receiver = receiver_session.clone();
    let empty_result = fresh_receiver.decrypt(&message_header, &empty_ciphertext);
    assert!(
        empty_result.is_err(),
        "Decryption should fail with empty ciphertext"
    );
    
    // Verify the error is about invalid input rather than decryption failure
    match empty_result.unwrap_err() {
        crate::error::ProtocolError::InvalidInput { details } => {
            assert!(
                details.contains("Cannot decrypt empty ciphertext"),
                "Error should indicate empty ciphertext, got: {}",
                details
            );
        }
        other_error => {
            panic!("Expected InvalidInput error for empty ciphertext, got: {:?}", other_error);
        }
    }

    // Step 7: Test edge case - ciphertext too short (less than nonce size)
    let short_ciphertext = vec![0x01, 0x02, 0x03]; // Only 3 bytes, need at least 12 for nonce
    let mut fresh_receiver = receiver_session.clone();
    let short_result = fresh_receiver.decrypt(&message_header, &short_ciphertext);
    assert!(
        short_result.is_err(),
        "Decryption should fail with ciphertext shorter than nonce"
    );
    
    // Verify the error is a decryption failure due to insufficient data
    match short_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Ciphertext too short"),
                "Error should indicate ciphertext too short, got: {}",
                reason
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure error for short ciphertext, got: {:?}", other_error);
        }
    }
}

/// Test for subtask 4.2: Test Decryption Failure with Malformed Header (Invalid Public Key)
///
/// This test verifies that a message is rejected if its header contains an incorrect
/// or unexpected Diffie-Hellman public key. This ensures that messages with corrupted
/// or malicious headers cannot be processed.
#[test]
fn test_decryption_failure_with_malformed_header_invalid_public_key() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("test-invalid-header-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("test-invalid-header-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message to set up cryptographic state
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Step 2: Create a legitimate message with known plaintext
    let test_plaintext = b"This is a test message with valid header";
    let (legitimate_header, legitimate_ciphertext) = sender_session.encrypt(test_plaintext)
        .expect("Test message encryption should succeed");

    // Verify the legitimate message can be decrypted successfully first
    let mut receiver_session_copy = receiver_session.clone();
    let legitimate_decrypted = receiver_session_copy.decrypt(&legitimate_header, &legitimate_ciphertext)
        .expect("Legitimate message decryption should succeed");
    assert_eq!(
        legitimate_decrypted,
        test_plaintext,
        "Legitimate message should decrypt correctly"
    );

    // Step 3: Test different types of invalid public keys in the header
    
    // Test 3a: Replace with a completely random public key
    let random_key = KeyPair::generate()
        .expect("Failed to generate random key")
        .public_key();
    
    let malformed_header_random = crate::protocol::MessageHeader {
        dh_public_key: random_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: legitimate_header.message_number,
    };

    let mut fresh_receiver = receiver_session.clone();
    let random_key_result = fresh_receiver.decrypt(&malformed_header_random, &legitimate_ciphertext);
    assert!(
        random_key_result.is_err(),
        "Decryption should fail with random public key in header"
    );

    // The error could be DecryptionFailure or InvalidMessageFormat depending on implementation
    match random_key_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message") || reason.contains("decryption"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        crate::error::ProtocolError::InvalidMessageFormat { details } => {
            // This is also acceptable as the header is malformed
            assert!(
                details.contains("key") || details.contains("header") || details.contains("message"),
                "Error should indicate invalid message format, got: {}",
                details
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure or InvalidMessageFormat error, got: {:?}", other_error);
        }
    }

    // Test 3b: Replace with a public key from a different session
    let different_key = KeyPair::from_seed(&[0xFFu8; 32])
        .expect("Failed to create different key")
        .public_key();
    
    let malformed_header_different = crate::protocol::MessageHeader {
        dh_public_key: different_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: legitimate_header.message_number,
    };

    let mut fresh_receiver = receiver_session.clone();
    let different_key_result = fresh_receiver.decrypt(&malformed_header_different, &legitimate_ciphertext);
    assert!(
        different_key_result.is_err(),
        "Decryption should fail with public key from different session"
    );

    // Verify the error type
    match different_key_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message") || reason.contains("decryption"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        crate::error::ProtocolError::InvalidMessageFormat { details } => {
            // This is also acceptable as the header is malformed
            assert!(
                details.contains("key") || details.contains("header") || details.contains("message"),
                "Error should indicate invalid message format, got: {}",
                details
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure or InvalidMessageFormat error, got: {:?}", other_error);
        }
    }

    // Test 3c: Use the receiver's own public key (should not work for decryption)
    let receiver_own_key = receiver_session.get_dh_public_key();
    
    let malformed_header_own = crate::protocol::MessageHeader {
        dh_public_key: receiver_own_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: legitimate_header.message_number,
    };

    let mut fresh_receiver = receiver_session.clone();
    let own_key_result = fresh_receiver.decrypt(&malformed_header_own, &legitimate_ciphertext);
    assert!(
        own_key_result.is_err(),
        "Decryption should fail when using receiver's own public key"
    );

    // Verify the error type
    match own_key_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message") || reason.contains("decryption"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        crate::error::ProtocolError::InvalidMessageFormat { details } => {
            // This is also acceptable as the header is malformed
            assert!(
                details.contains("key") || details.contains("header") || details.contains("message"),
                "Error should indicate invalid message format, got: {}",
                details
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure or InvalidMessageFormat error, got: {:?}", other_error);
        }
    }

    // Test 3d: Create a public key with invalid format (all zeros)
    // This tests the case where the public key is structurally invalid
    let invalid_key = crate::crypto::keys::PublicKey::from_bytes([0u8; 32]);
    
    let malformed_header_invalid = crate::protocol::MessageHeader {
        dh_public_key: invalid_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: legitimate_header.message_number,
    };

    let mut fresh_receiver = receiver_session.clone();
    let invalid_key_result = fresh_receiver.decrypt(&malformed_header_invalid, &legitimate_ciphertext);
    assert!(
        invalid_key_result.is_err(),
        "Decryption should fail when using structurally invalid public key"
    );

    // Verify the error type
    match invalid_key_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message") || reason.contains("decryption"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        crate::error::ProtocolError::InvalidMessageFormat { details } => {
            // This is also acceptable as the header is malformed
            assert!(
                details.contains("key") || details.contains("header") || details.contains("message"),
                "Error should indicate invalid message format, got: {}",
                details
            );
        }
        crate::error::ProtocolError::KeyDerivationFailure { operation: _, reason } => {
            // This is also acceptable as key derivation may fail with invalid keys
            assert!(
                reason.contains("key") || reason.contains("derivation"),
                "Error should indicate key derivation failure, got: {}",
                reason
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure, InvalidMessageFormat, or KeyDerivationFailure error, got: {:?}", other_error);
        }
    }

    // Step 4: Verify that the receiver session state is not corrupted by failed decryption attempts
    // The session should still be able to decrypt legitimate messages
    let second_test_plaintext = b"Second test message after invalid header attempts";
    let (second_header, second_ciphertext) = sender_session.encrypt(second_test_plaintext)
        .expect("Second message encryption should succeed");

    let second_decrypted = receiver_session.decrypt(&second_header, &second_ciphertext)
        .expect("Second message decryption should succeed after failed header attempts");
    assert_eq!(
        second_decrypted,
        second_test_plaintext,
        "Session should still work normally after failed decryption attempts"
    );

    // Step 5: Verify session health after invalid header tests
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages after invalid header tests"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages after invalid header tests"
    );

    // Step 6: Test edge case - ensure legitimate messages still work with correct headers
    // This verifies that our test setup is correct and we're not breaking legitimate functionality
    let final_test_plaintext = b"Final verification message";
    let (final_header, final_ciphertext) = sender_session.encrypt(final_test_plaintext)
        .expect("Final message encryption should succeed");

    let final_decrypted = receiver_session.decrypt(&final_header, &final_ciphertext)
        .expect("Final message decryption should succeed");
    assert_eq!(
        final_decrypted,
        final_test_plaintext,
        "Legitimate messages should continue to work correctly"
    );

    // Step 7: Verify that session counters and state are maintained correctly
    assert!(
        receiver_session.receiving_message_number > 0,
        "Receiving message number should have advanced"
    );
    assert!(
        sender_session.sending_message_number > 0,
        "Sending message number should have advanced"
    );
}

/// Test for subtask 4.3: Test Decryption Failure with Malformed Header (Invalid Message Number)
///
/// This test verifies that decryption fails if a message header contains a message number
/// that is out of the valid range or doesn't align with the current state. This protects
/// against replay attacks and ensures message ordering integrity.
#[test]
fn test_decryption_failure_with_malformed_header_invalid_message_number() {
    // Setup: Create both initiator and responder sessions with deterministic keys
    let x3dh_secret = [0x42u8; 32];
    
    // Create fixed keys for deterministic testing
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create remote identity key");
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create remote signed prekey");
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create responder identity key");

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        5, // Small max_skip for easier testing
        Some("test-invalid-message-number-sender".to_string()),
    )
    .expect("Failed to create sender session");

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        5, // Small max_skip for easier testing
        Some("test-invalid-message-number-receiver".to_string()),
    )
    .expect("Failed to create receiver session");

    // Step 1: Establish the session with an initial message to set up cryptographic state
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)
        .expect("Initial message encryption should succeed");
    
    let initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)
        .expect("Initial message decryption should succeed");
    assert_eq!(initial_decrypted, initial_message);

    // Step 2: Create a legitimate message with known plaintext
    let test_plaintext = b"This is a test message with valid header";
    let (legitimate_header, legitimate_ciphertext) = sender_session.encrypt(test_plaintext)
        .expect("Test message encryption should succeed");

    // Verify the legitimate message can be decrypted successfully first
    let mut receiver_session_copy = receiver_session.clone();
    let legitimate_decrypted = receiver_session_copy.decrypt(&legitimate_header, &legitimate_ciphertext)
        .expect("Legitimate message decryption should succeed");
    assert_eq!(
        legitimate_decrypted,
        test_plaintext,
        "Legitimate message should decrypt correctly"
    );

    // Step 3: Test different types of invalid message numbers in the header
    
    // Test 3a: Message number far in the future (beyond max_skip limit)
    let far_future_header = crate::protocol::MessageHeader {
        dh_public_key: legitimate_header.dh_public_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: receiver_session.receiving_message_number + receiver_session.max_skip + 10,
    };

    let mut fresh_receiver = receiver_session.clone();
    let far_future_result = fresh_receiver.decrypt(&far_future_header, &legitimate_ciphertext);
    assert!(
        far_future_result.is_err(),
        "Decryption should fail with message number far in the future"
    );

    // Verify the error type - should be MessageOutOfOrder due to exceeding skip limit
    match far_future_result.unwrap_err() {
        crate::error::ProtocolError::MessageOutOfOrder { received, expected } => {
            assert_eq!(received, far_future_header.message_number);
            assert_eq!(expected, fresh_receiver.receiving_message_number);
        }
        other_error => {
            panic!("Expected MessageOutOfOrder error, got: {:?}", other_error);
        }
    }

    // Test 3b: Message number with maximum u32 value (extreme case)
    let max_u32_header = crate::protocol::MessageHeader {
        dh_public_key: legitimate_header.dh_public_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: u32::MAX,
    };

    let mut fresh_receiver = receiver_session.clone();
    let max_u32_result = fresh_receiver.decrypt(&max_u32_header, &legitimate_ciphertext);
    assert!(
        max_u32_result.is_err(),
        "Decryption should fail with maximum u32 message number"
    );

    // Verify the error type
    match max_u32_result.unwrap_err() {
        crate::error::ProtocolError::MessageOutOfOrder { received, expected } => {
            assert_eq!(received, u32::MAX);
            assert_eq!(expected, fresh_receiver.receiving_message_number);
        }
        other_error => {
            panic!("Expected MessageOutOfOrder error, got: {:?}", other_error);
        }
    }

    // Test 3c: Message number that would cause integer overflow in calculations
    let overflow_header = crate::protocol::MessageHeader {
        dh_public_key: legitimate_header.dh_public_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: u32::MAX - 1,
    };

    let mut fresh_receiver = receiver_session.clone();
    let overflow_result = fresh_receiver.decrypt(&overflow_header, &legitimate_ciphertext);
    assert!(
        overflow_result.is_err(),
        "Decryption should fail with message number that could cause overflow"
    );

    // Verify the error type
    match overflow_result.unwrap_err() {
        crate::error::ProtocolError::MessageOutOfOrder { received, expected } => {
            assert_eq!(received, u32::MAX - 1);
            assert_eq!(expected, fresh_receiver.receiving_message_number);
        }
        other_error => {
            panic!("Expected MessageOutOfOrder error, got: {:?}", other_error);
        }
    }

    // Test 3d: Message number that's exactly at the skip limit boundary
    let boundary_message_number = receiver_session.receiving_message_number + receiver_session.max_skip;
    let boundary_header = crate::protocol::MessageHeader {
        dh_public_key: legitimate_header.dh_public_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: boundary_message_number,
    };

    let mut fresh_receiver = receiver_session.clone();
    let boundary_result = fresh_receiver.decrypt(&boundary_header, &legitimate_ciphertext);
    
    // This should succeed as it's exactly at the limit
    if boundary_result.is_err() {
        // If it fails, verify it's due to decryption failure (wrong ciphertext for this message number)
        match boundary_result.unwrap_err() {
            crate::error::ProtocolError::DecryptionFailure { reason } => {
                assert!(
                    reason.contains("Failed to decrypt message"),
                    "Error should indicate decryption failure, got: {}",
                    reason
                );
            }
            other_error => {
                panic!("Expected DecryptionFailure error for boundary case, got: {:?}", other_error);
            }
        }
    }

    // Test 3e: Message number just beyond the skip limit boundary
    let beyond_boundary_header = crate::protocol::MessageHeader {
        dh_public_key: legitimate_header.dh_public_key,
        previous_chain_length: legitimate_header.previous_chain_length,
        message_number: boundary_message_number + 1,
    };

    let mut fresh_receiver = receiver_session.clone();
    let beyond_boundary_result = fresh_receiver.decrypt(&beyond_boundary_header, &legitimate_ciphertext);
    assert!(
        beyond_boundary_result.is_err(),
        "Decryption should fail with message number just beyond skip limit"
    );

    // Verify the error type
    match beyond_boundary_result.unwrap_err() {
        crate::error::ProtocolError::MessageOutOfOrder { received, expected } => {
            assert_eq!(received, boundary_message_number + 1);
            assert_eq!(expected, fresh_receiver.receiving_message_number);
        }
        other_error => {
            panic!("Expected MessageOutOfOrder error, got: {:?}", other_error);
        }
    }

    // Step 4: Verify that the receiver session state is not corrupted by failed decryption attempts
    // The session should still be able to decrypt legitimate messages
    let second_test_plaintext = b"Second test message after invalid message number attempts";
    let (second_header, second_ciphertext) = sender_session.encrypt(second_test_plaintext)
        .expect("Second message encryption should succeed");

    let second_decrypted = receiver_session.decrypt(&second_header, &second_ciphertext)
        .expect("Second message decryption should succeed after failed message number attempts");
    assert_eq!(
        second_decrypted,
        second_test_plaintext,
        "Session should still work normally after failed decryption attempts"
    );

    // Step 5: Verify session health after invalid message number tests
    assert!(
        receiver_session.can_receive(),
        "Receiver should still be able to receive messages after invalid message number tests"
    );
    assert!(
        receiver_session.can_send(),
        "Receiver should still be able to send messages after invalid message number tests"
    );

    // Step 6: Test that legitimate out-of-order messages within the skip limit still work
    // This ensures our tests don't break legitimate functionality
    let msg1_plaintext = b"Message 1 - will arrive second";
    let msg2_plaintext = b"Message 2 - will arrive first";

    let (msg1_header, msg1_ciphertext) = sender_session.encrypt(msg1_plaintext)
        .expect("Message 1 encryption should succeed");
    
    let (msg2_header, msg2_ciphertext) = sender_session.encrypt(msg2_plaintext)
        .expect("Message 2 encryption should succeed");

    // Deliver msg2 first (legitimate out-of-order within skip limit)
    let msg2_decrypted = receiver_session.decrypt(&msg2_header, &msg2_ciphertext)
        .expect("Message 2 decryption should succeed (legitimate out-of-order)");
    assert_eq!(msg2_decrypted, msg2_plaintext);

    // Then deliver msg1 (delayed message)
    let msg1_decrypted = receiver_session.decrypt(&msg1_header, &msg1_ciphertext)
        .expect("Message 1 decryption should succeed using stored key");
    assert_eq!(msg1_decrypted, msg1_plaintext);

    // Step 7: Verify final session state
    assert!(
        receiver_session.receiving_message_number > 0,
        "Receiving message number should have advanced"
    );
    assert!(
        sender_session.sending_message_number > 0,
        "Sending message number should have advanced"
    );
    // Note: We may have skipped keys remaining from the out-of-order test, which is expected
    assert!(
        receiver_session.skipped_keys_count() <= 1,
        "Should have at most 1 skipped key remaining from out-of-order test"
    );
}

/// Helper function to create a session in a complex state with skipped message keys
/// 
/// This function establishes a session and exchanges several messages in a way that
/// creates skipped message keys, resulting in a complex session state suitable for
/// serialization testing.
fn create_complex_session_state() -> Result<state::SessionState, crate::error::ProtocolError> {
    // Setup deterministic keys for reproducible testing
    let x3dh_secret = [0x42u8; 32];
    let remote_identity = KeyPair::from_seed(&[0x01u8; 32])?;
    let remote_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])?;
    let responder_identity = KeyPair::from_seed(&[0x03u8; 32])?;

    // Create initiator session (sender)
    let remote_bundle = PreKeyBundle {
        identity_key: remote_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: remote_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut sender_session = state::SessionState::new_initiator(
        x3dh_secret,
        &remote_bundle,
        1000, // max_skip
        Some("complex-test-sender".to_string()),
    )?;

    // Create responder session (receiver)
    let mut receiver_session = state::SessionState::new_responder(
        x3dh_secret,
        remote_signed_prekey.clone(),
        responder_identity.public_key(),
        1000, // max_skip
        Some("complex-test-receiver".to_string()),
    )?;

    // Establish the session with an initial message
    let initial_message = b"Initial message to establish session";
    let (initial_header, initial_ciphertext) = sender_session.encrypt(initial_message)?;
    let _initial_decrypted = receiver_session.decrypt(&initial_header, &initial_ciphertext)?;

    // Create a complex state by sending multiple messages out of order
    // This will create skipped message keys in the receiver session
    
    // Encrypt several messages in sequence
    let msg1_plaintext = b"Message 1 - will be delayed";
    let msg2_plaintext = b"Message 2 - will also be delayed";
    let msg3_plaintext = b"Message 3 - will arrive first";
    let msg4_plaintext = b"Message 4 - normal order";

    let (msg1_header, msg1_ciphertext) = sender_session.encrypt(msg1_plaintext)?;
    let (_msg2_header, _msg2_ciphertext) = sender_session.encrypt(msg2_plaintext)?;
    let (msg3_header, msg3_ciphertext) = sender_session.encrypt(msg3_plaintext)?;
    let (_msg4_header, _msg4_ciphertext) = sender_session.encrypt(msg4_plaintext)?;

    // Deliver msg3 first (out of order) - this creates skipped keys for msg1 and msg2
    let _msg3_decrypted = receiver_session.decrypt(&msg3_header, &msg3_ciphertext)?;

    // Now deliver msg1 (delayed message) - this consumes one skipped key
    let _msg1_decrypted = receiver_session.decrypt(&msg1_header, &msg1_ciphertext)?;

    // Leave msg2 as a skipped key to create a complex state
    // The session now has:
    // - Advanced message counters
    // - One remaining skipped key for msg2
    // - Established sending and receiving chains
    // - Activity timestamps
    // - Session metadata

    // Add some additional complexity by sending a few more messages
    let additional_msg1 = b"Additional message 1";
    let additional_msg2 = b"Additional message 2";
    
    let (add_header1, add_ciphertext1) = sender_session.encrypt(additional_msg1)?;
    let (add_header2, add_ciphertext2) = sender_session.encrypt(additional_msg2)?;
    
    let _add_decrypted1 = receiver_session.decrypt(&add_header1, &add_ciphertext1)?;
    let _add_decrypted2 = receiver_session.decrypt(&add_header2, &add_ciphertext2)?;

    // Now have the receiver send some messages back to create a bidirectional complex state
    let reply_msg1 = b"Reply message 1 from receiver";
    let reply_msg2 = b"Reply message 2 from receiver";
    
    let (reply_header1, reply_ciphertext1) = receiver_session.encrypt(reply_msg1)?;
    let (reply_header2, reply_ciphertext2) = receiver_session.encrypt(reply_msg2)?;
    
    let _reply_decrypted1 = sender_session.decrypt(&reply_header1, &reply_ciphertext1)?;
    let _reply_decrypted2 = sender_session.decrypt(&reply_header2, &reply_ciphertext2)?;

    // The receiver session now has:
    // - Multiple messages processed (both sent and received)
    // - One skipped key remaining (for msg2)
    // - Advanced sending and receiving counters
    // - Complex cryptographic state
    // - Metadata with timestamps
    // - Bidirectional communication established

    Ok(receiver_session)
}

/// Test for subtask 5.1: Test JSON Round-Trip Serialization for Complex Session State
///
/// This test verifies that a session, advanced to a complex state with skipped messages,
/// can be serialized to JSON and deserialized back into an identical object without data loss.
#[test]
fn test_json_round_trip_serialization_for_complex_session_state() {
    // Create a complex session state using the helper function
    let original_session = create_complex_session_state()
        .expect("Failed to create complex session state");

    // Verify the session is in a complex state before serialization
    assert!(
        original_session.can_send(),
        "Original session should be able to send messages"
    );
    assert!(
        original_session.can_receive(),
        "Original session should be able to receive messages"
    );
    assert!(
        original_session.skipped_keys_count() > 0,
        "Original session should have skipped message keys"
    );
    assert!(
        original_session.sending_message_number > 0,
        "Original session should have sent messages"
    );
    assert!(
        original_session.receiving_message_number > 0,
        "Original session should have received messages"
    );
    assert!(
        original_session.last_activity.is_some(),
        "Original session should have activity timestamp"
    );
    assert!(
        original_session.session_id.is_some(),
        "Original session should have session ID"
    );

    // Serialize the session to JSON
    let json_serialized = original_session.to_json()
        .expect("Session serialization to JSON should succeed");

    // Verify the JSON is not empty and contains expected fields
    assert!(
        !json_serialized.is_empty(),
        "Serialized JSON should not be empty"
    );
    assert!(
        json_serialized.contains("root_key"),
        "JSON should contain root_key field"
    );
    assert!(
        json_serialized.contains("dh_ratchet_key"),
        "JSON should contain dh_ratchet_key field"
    );
    assert!(
        json_serialized.contains("sending_message_number"),
        "JSON should contain sending_message_number field"
    );
    assert!(
        json_serialized.contains("receiving_message_number"),
        "JSON should contain receiving_message_number field"
    );
    assert!(
        json_serialized.contains("skipped_message_keys"),
        "JSON should contain skipped_message_keys field"
    );
    assert!(
        json_serialized.contains("session_id"),
        "JSON should contain session_id field"
    );

    // Deserialize the JSON back into a session object
    let deserialized_session = state::SessionState::from_json(&json_serialized)
        .expect("Session deserialization from JSON should succeed");

    // Verify that the deserialized session is identical to the original
    
    // Check cryptographic state
    assert_eq!(
        deserialized_session.root_key,
        original_session.root_key,
        "Root key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.dh_ratchet_key.private_key().as_bytes(),
        original_session.dh_ratchet_key.private_key().as_bytes(),
        "DH ratchet private key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.dh_ratchet_key.public_key().as_bytes(),
        original_session.dh_ratchet_key.public_key().as_bytes(),
        "DH ratchet public key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.remote_dh_public_key,
        original_session.remote_dh_public_key,
        "Remote DH public key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.sending_chain_key,
        original_session.sending_chain_key,
        "Sending chain key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.receiving_chain_key,
        original_session.receiving_chain_key,
        "Receiving chain key should be identical after round-trip"
    );

    // Check message counters
    assert_eq!(
        deserialized_session.sending_message_number,
        original_session.sending_message_number,
        "Sending message number should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.receiving_message_number,
        original_session.receiving_message_number,
        "Receiving message number should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.previous_chain_length,
        original_session.previous_chain_length,
        "Previous chain length should be identical after round-trip"
    );

    // Check skipped message keys
    assert_eq!(
        deserialized_session.skipped_keys_count(),
        original_session.skipped_keys_count(),
        "Skipped keys count should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.skipped_message_keys,
        original_session.skipped_message_keys,
        "Skipped message keys should be identical after round-trip"
    );

    // Check configuration
    assert_eq!(
        deserialized_session.max_skip,
        original_session.max_skip,
        "Max skip should be identical after round-trip"
    );

    // Check metadata
    assert_eq!(
        deserialized_session.remote_identity_key,
        original_session.remote_identity_key,
        "Remote identity key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.local_identity_key,
        original_session.local_identity_key,
        "Local identity key should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.created_at,
        original_session.created_at,
        "Created timestamp should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.last_activity,
        original_session.last_activity,
        "Last activity timestamp should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.session_id,
        original_session.session_id,
        "Session ID should be identical after round-trip"
    );

    // Check functional capabilities
    assert_eq!(
        deserialized_session.can_send(),
        original_session.can_send(),
        "Send capability should be identical after round-trip"
    );
    assert_eq!(
        deserialized_session.can_receive(),
        original_session.can_receive(),
        "Receive capability should be identical after round-trip"
    );

    // Verify that the deserialized session is functionally equivalent
    // by testing that it can still process the remaining skipped message
    if original_session.skipped_keys_count() > 0 {
        // Create a copy of the original session to get the skipped message
        let mut original_copy = original_session.clone();
        let mut deserialized_copy = deserialized_session.clone();
        
        // Both sessions should have the same functional state
        assert_eq!(
            original_copy.skipped_keys_count(),
            deserialized_copy.skipped_keys_count(),
            "Both sessions should have the same number of skipped keys"
        );
    }

    // Test that both sessions can encrypt new messages identically
    // (Note: Due to random nonces, ciphertexts will differ, but both should succeed)
    let test_message = b"Test message after deserialization";
    
    let mut original_copy = original_session.clone();
    let mut deserialized_copy = deserialized_session.clone();
    
    let original_encrypt_result = original_copy.encrypt(test_message);
    let deserialized_encrypt_result = deserialized_copy.encrypt(test_message);
    
    assert!(
        original_encrypt_result.is_ok(),
        "Original session should be able to encrypt after round-trip test"
    );
    assert!(
        deserialized_encrypt_result.is_ok(),
        "Deserialized session should be able to encrypt after round-trip test"
    );

    // Verify that message headers have the same structure
    let (original_header, _) = original_encrypt_result.unwrap();
    let (deserialized_header, _) = deserialized_encrypt_result.unwrap();
    
    assert_eq!(
        original_header.message_number,
        deserialized_header.message_number,
        "Message numbers should be identical for both sessions"
    );
    assert_eq!(
        original_header.previous_chain_length,
        deserialized_header.previous_chain_length,
        "Previous chain lengths should be identical for both sessions"
    );
    assert_eq!(
        original_header.dh_public_key.as_bytes(),
        deserialized_header.dh_public_key.as_bytes(),
        "DH public keys should be identical for both sessions"
    );
}

/// Test for subtask 5.2: Test Binary Round-Trip Serialization for Complex Session State
///
/// This test verifies that a session in a complex state can be serialized to a byte array
/// and deserialized back into an identical object, ensuring the binary format is stable and complete.
#[test]
fn test_binary_round_trip_serialization_for_complex_session_state() {
    // Create a complex session state using the helper function
    let original_session = create_complex_session_state()
        .expect("Failed to create complex session state");

    // Verify the session is in a complex state before serialization
    assert!(
        original_session.can_send(),
        "Original session should be able to send messages"
    );
    assert!(
        original_session.can_receive(),
        "Original session should be able to receive messages"
    );
    assert!(
        original_session.skipped_keys_count() > 0,
        "Original session should have skipped message keys"
    );
    assert!(
        original_session.sending_message_number > 0,
        "Original session should have sent messages"
    );
    assert!(
        original_session.receiving_message_number > 0,
        "Original session should have received messages"
    );
    assert!(
        original_session.last_activity.is_some(),
        "Original session should have activity timestamp"
    );
    assert!(
        original_session.session_id.is_some(),
        "Original session should have session ID"
    );

    // Serialize the session to bytes
    let bytes_serialized = original_session.to_bytes()
        .expect("Session serialization to bytes should succeed");

    // Verify the byte array is not empty and has reasonable size
    assert!(
        !bytes_serialized.is_empty(),
        "Serialized bytes should not be empty"
    );
    assert!(
        bytes_serialized.len() > 100,
        "Serialized bytes should have substantial size for complex session (got {} bytes)",
        bytes_serialized.len()
    );

    // Verify the byte array contains valid UTF-8 (since it's JSON internally)
    let bytes_as_string = std::str::from_utf8(&bytes_serialized)
        .expect("Serialized bytes should be valid UTF-8");
    
    // Verify it contains expected JSON fields (since to_bytes uses JSON internally)
    assert!(
        bytes_as_string.contains("root_key"),
        "Binary serialization should contain root_key field"
    );
    assert!(
        bytes_as_string.contains("dh_ratchet_key"),
        "Binary serialization should contain dh_ratchet_key field"
    );
    assert!(
        bytes_as_string.contains("skipped_message_keys"),
        "Binary serialization should contain skipped_message_keys field"
    );

    // Deserialize the bytes back into a session object
    let deserialized_session = state::SessionState::from_bytes(&bytes_serialized)
        .expect("Session deserialization from bytes should succeed");

    // Verify that the deserialized session is identical to the original
    
    // Check cryptographic state
    assert_eq!(
        deserialized_session.root_key,
        original_session.root_key,
        "Root key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.dh_ratchet_key.private_key().as_bytes(),
        original_session.dh_ratchet_key.private_key().as_bytes(),
        "DH ratchet private key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.dh_ratchet_key.public_key().as_bytes(),
        original_session.dh_ratchet_key.public_key().as_bytes(),
        "DH ratchet public key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.remote_dh_public_key,
        original_session.remote_dh_public_key,
        "Remote DH public key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.sending_chain_key,
        original_session.sending_chain_key,
        "Sending chain key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.receiving_chain_key,
        original_session.receiving_chain_key,
        "Receiving chain key should be identical after binary round-trip"
    );

    // Check message counters
    assert_eq!(
        deserialized_session.sending_message_number,
        original_session.sending_message_number,
        "Sending message number should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.receiving_message_number,
        original_session.receiving_message_number,
        "Receiving message number should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.previous_chain_length,
        original_session.previous_chain_length,
        "Previous chain length should be identical after binary round-trip"
    );

    // Check skipped message keys
    assert_eq!(
        deserialized_session.skipped_keys_count(),
        original_session.skipped_keys_count(),
        "Skipped keys count should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.skipped_message_keys,
        original_session.skipped_message_keys,
        "Skipped message keys should be identical after binary round-trip"
    );

    // Check configuration
    assert_eq!(
        deserialized_session.max_skip,
        original_session.max_skip,
        "Max skip should be identical after binary round-trip"
    );

    // Check metadata
    assert_eq!(
        deserialized_session.remote_identity_key,
        original_session.remote_identity_key,
        "Remote identity key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.local_identity_key,
        original_session.local_identity_key,
        "Local identity key should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.created_at,
        original_session.created_at,
        "Created timestamp should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.last_activity,
        original_session.last_activity,
        "Last activity timestamp should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.session_id,
        original_session.session_id,
        "Session ID should be identical after binary round-trip"
    );

    // Check functional capabilities
    assert_eq!(
        deserialized_session.can_send(),
        original_session.can_send(),
        "Send capability should be identical after binary round-trip"
    );
    assert_eq!(
        deserialized_session.can_receive(),
        original_session.can_receive(),
        "Receive capability should be identical after binary round-trip"
    );

    // Test that both sessions can encrypt new messages identically
    // (Note: Due to random nonces, ciphertexts will differ, but both should succeed)
    let test_message = b"Test message after binary deserialization";
    
    let mut original_copy = original_session.clone();
    let mut deserialized_copy = deserialized_session.clone();
    
    let original_encrypt_result = original_copy.encrypt(test_message);
    let deserialized_encrypt_result = deserialized_copy.encrypt(test_message);
    
    assert!(
        original_encrypt_result.is_ok(),
        "Original session should be able to encrypt after binary round-trip test"
    );
    assert!(
        deserialized_encrypt_result.is_ok(),
        "Deserialized session should be able to encrypt after binary round-trip test"
    );

    // Verify that message headers have the same structure
    let (original_header, _) = original_encrypt_result.unwrap();
    let (deserialized_header, _) = deserialized_encrypt_result.unwrap();
    
    assert_eq!(
        original_header.message_number,
        deserialized_header.message_number,
        "Message numbers should be identical for both sessions after binary round-trip"
    );
    assert_eq!(
        original_header.previous_chain_length,
        deserialized_header.previous_chain_length,
        "Previous chain lengths should be identical for both sessions after binary round-trip"
    );
    assert_eq!(
        original_header.dh_public_key.as_bytes(),
        deserialized_header.dh_public_key.as_bytes(),
        "DH public keys should be identical for both sessions after binary round-trip"
    );

    // Verify that binary and JSON serialization produce equivalent results
    let json_serialized = original_session.to_json()
        .expect("JSON serialization should succeed");
    let json_as_bytes = json_serialized.into_bytes();
    
    assert_eq!(
        bytes_serialized,
        json_as_bytes,
        "Binary serialization should produce the same result as JSON serialization converted to bytes"
    );

    // Test serialized size method
    let reported_size = original_session.serialized_size()
        .expect("Serialized size calculation should succeed");
    assert_eq!(
        reported_size,
        bytes_serialized.len(),
        "Reported serialized size should match actual serialized size"
    );
}

/// Test for subtask 5.3: Test Deserialization Failure on Corrupted JSON Input
///
/// This test ensures that the JSON deserialization function (`from_json`) is robust
/// and fails gracefully when provided with malformed or incomplete data.
#[test]
fn test_deserialization_failure_on_corrupted_json_input() {
    // First, create a valid session state and serialize it to get a baseline
    let original_session = create_complex_session_state()
        .expect("Failed to create complex session state");
    
    let valid_json = original_session.to_json()
        .expect("Failed to serialize session to JSON");
    
    // Verify that the valid JSON deserializes correctly
    let valid_deserialization = state::SessionState::from_json(&valid_json);
    assert!(
        valid_deserialization.is_ok(),
        "Valid JSON should deserialize successfully"
    );

    // Test Case 1: Truncated JSON string midway
    let truncated_json = &valid_json[..valid_json.len() / 2];
    let truncated_result = state::SessionState::from_json(truncated_json);
    assert!(
        truncated_result.is_err(),
        "Truncated JSON should fail to deserialize"
    );
    
    // Verify it returns a deserialization error
    match truncated_result {
        Err(crate::error::ProtocolError::DeserializationError { .. }) => {
            // Expected error type
        }
        Err(other_error) => {
            panic!("Expected DeserializationError, got: {:?}", other_error);
        }
        Ok(_) => {
            panic!("Truncated JSON should not deserialize successfully");
        }
    }

    // Test Case 2: Change a critical value to an invalid format
    // Replace the root_key array with an invalid value
    let invalid_root_key_json = valid_json.replace(
        "\"root_key\":[", 
        "\"root_key\":\"invalid_string_instead_of_array\","
    );
    let invalid_root_key_result = state::SessionState::from_json(&invalid_root_key_json);
    assert!(
        invalid_root_key_result.is_err(),
        "JSON with invalid root_key format should fail to deserialize"
    );

    // Test Case 3: Remove a required field (root_key)
    // Find and remove the root_key field entirely
    let root_key_start = valid_json.find("\"root_key\"").expect("Should find root_key field");
    let root_key_end = valid_json[root_key_start..].find("],").expect("Should find end of root_key array") + root_key_start + 2;
    let mut missing_field_json = String::new();
    missing_field_json.push_str(&valid_json[..root_key_start]);
    missing_field_json.push_str(&valid_json[root_key_end + 1..]); // Skip the comma too
    
    let missing_field_result = state::SessionState::from_json(&missing_field_json);
    assert!(
        missing_field_result.is_err(),
        "JSON missing required root_key field should fail to deserialize"
    );

    // Test Case 4: Invalid JSON syntax (missing closing brace)
    let unclosed_json = &valid_json[..valid_json.len() - 1]; // Remove last character
    let unclosed_result = state::SessionState::from_json(unclosed_json);
    assert!(
        unclosed_result.is_err(),
        "JSON with missing closing brace should fail to deserialize"
    );

    // Test Case 5: Completely invalid JSON (not JSON at all)
    let not_json = "This is not JSON at all, just plain text";
    let not_json_result = state::SessionState::from_json(not_json);
    assert!(
        not_json_result.is_err(),
        "Non-JSON string should fail to deserialize"
    );

    // Test Case 6: Empty string
    let empty_result = state::SessionState::from_json("");
    assert!(
        empty_result.is_err(),
        "Empty string should fail to deserialize"
    );

    // Test Case 7: Valid JSON but wrong structure (array instead of object)
    let array_json = "[1, 2, 3, 4, 5]";
    let array_result = state::SessionState::from_json(array_json);
    assert!(
        array_result.is_err(),
        "JSON array should fail to deserialize as SessionState"
    );

    // Test Case 8: Valid JSON object but missing all required fields
    let minimal_object_json = "{}";
    let minimal_object_result = state::SessionState::from_json(minimal_object_json);
    assert!(
        minimal_object_result.is_err(),
        "Empty JSON object should fail to deserialize as SessionState"
    );

    // Test Case 9: Corrupt a nested field (dh_ratchet_key)
    let invalid_dh_key_json = valid_json.replace(
        "\"dh_ratchet_key\":{",
        "\"dh_ratchet_key\":null,"
    );
    let invalid_dh_key_result = state::SessionState::from_json(&invalid_dh_key_json);
    assert!(
        invalid_dh_key_result.is_err(),
        "JSON with null dh_ratchet_key should fail to deserialize"
    );

    // Test Case 10: Invalid numeric value (negative message number)
    let invalid_number_json = valid_json.replace(
        "\"sending_message_number\":",
        "\"sending_message_number\":-1,"
    );
    // Note: This might actually succeed since serde might handle negative numbers,
    // but let's test it anyway
    let invalid_number_result = state::SessionState::from_json(&invalid_number_json);
    // We don't assert failure here since u32 might accept and convert negative numbers

    // Test Case 11: Malformed array in skipped_message_keys
    let invalid_skipped_keys_json = valid_json.replace(
        "\"skipped_message_keys\":{",
        "\"skipped_message_keys\":[\"invalid\"],"
    );
    let invalid_skipped_keys_result = state::SessionState::from_json(&invalid_skipped_keys_json);
    assert!(
        invalid_skipped_keys_result.is_err(),
        "JSON with invalid skipped_message_keys format should fail to deserialize"
    );

    // Verify that all error cases return the expected error type
    let test_cases = vec![
        ("truncated", truncated_result),
        ("invalid_root_key", invalid_root_key_result),
        ("missing_field", missing_field_result),
        ("unclosed", unclosed_result),
        ("not_json", not_json_result),
        ("empty", empty_result),
        ("array", array_result),
        ("minimal_object", minimal_object_result),
        ("invalid_dh_key", invalid_dh_key_result),
        ("invalid_skipped_keys", invalid_skipped_keys_result),
    ];

    for (test_name, result) in test_cases {
        match result {
            Err(crate::error::ProtocolError::DeserializationError { .. }) => {
                // Expected error type - test passes
            }
            Err(other_error) => {
                println!("Test case '{}' returned unexpected error type: {:?}", test_name, other_error);
                // Some error types might be acceptable depending on the specific corruption
                // The important thing is that it fails gracefully rather than panicking
            }
            Ok(_) => {
                panic!("Test case '{}' should have failed but succeeded", test_name);
            }
        }
    }

    // Additional verification: Ensure that the function doesn't panic on any input
    // Test with various edge cases that might cause panics
    let panic_test_cases = vec![
        "null",
        "true",
        "false",
        "123",
        "\"string\"",
        "{\"root_key\": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32]}",
        "{\"root_key\": [1,2,3]}",  // Too short array
        "{\"root_key\": [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33]}",  // Too long array
    ];

    for test_input in panic_test_cases {
        let result = std::panic::catch_unwind(|| {
            state::SessionState::from_json(test_input)
        });
        
        assert!(
            result.is_ok(),
            "from_json should not panic on input: {}",
            test_input
        );
        
        // The function should return an error, not panic
        if let Ok(deserialization_result) = result {
            assert!(
                deserialization_result.is_err(),
                "Malformed input '{}' should result in an error, not success",
                test_input
            );
        }
    }
}

/// Test for subtask 5.4: Test Deserialization Failure on Corrupted Binary Input
///
/// This test ensures that the binary deserialization function (`from_bytes`) correctly
/// rejects corrupted or incomplete byte arrays by returning an error.
#[test]
fn test_deserialization_failure_on_corrupted_binary_input() {
    // First, create a valid session state and serialize it to get a baseline
    let original_session = create_complex_session_state()
        .expect("Failed to create complex session state");
    
    let valid_bytes = original_session.to_bytes()
        .expect("Failed to serialize session to bytes");
    
    // Verify that the valid bytes deserialize correctly
    let valid_deserialization = state::SessionState::from_bytes(&valid_bytes);
    assert!(
        valid_deserialization.is_ok(),
        "Valid bytes should deserialize successfully"
    );

    // Test Case 1: Truncated byte array (removing the last few bytes)
    let truncated_bytes = &valid_bytes[..valid_bytes.len() - 10];
    let truncated_result = state::SessionState::from_bytes(truncated_bytes);
    assert!(
        truncated_result.is_err(),
        "Truncated byte array should fail to deserialize"
    );

    // Test Case 2: Truncated byte array (removing half the bytes)
    let half_truncated_bytes = &valid_bytes[..valid_bytes.len() / 2];
    let half_truncated_result = state::SessionState::from_bytes(half_truncated_bytes);
    assert!(
        half_truncated_result.is_err(),
        "Half-truncated byte array should fail to deserialize"
    );

    // Test Case 3: Byte array where bits in the middle have been flipped
    let mut bit_flipped_bytes = valid_bytes.clone();
    if bit_flipped_bytes.len() > 10 {
        // Flip some bits in the middle of the byte array
        let middle_index = bit_flipped_bytes.len() / 2;
        bit_flipped_bytes[middle_index] ^= 0xFF; // Flip all bits in one byte
        bit_flipped_bytes[middle_index + 1] ^= 0x55; // Flip alternating bits
        bit_flipped_bytes[middle_index + 2] ^= 0xAA; // Flip other alternating bits
    }
    let bit_flipped_result = state::SessionState::from_bytes(&bit_flipped_bytes);
    assert!(
        bit_flipped_result.is_err(),
        "Bit-flipped byte array should fail to deserialize"
    );

    // Test Case 4: Random byte corruption at the beginning
    let mut beginning_corrupted_bytes = valid_bytes.clone();
    if beginning_corrupted_bytes.len() > 5 {
        beginning_corrupted_bytes[0] = 0xFF;
        beginning_corrupted_bytes[1] = 0x00;
        beginning_corrupted_bytes[2] = 0xFF;
        beginning_corrupted_bytes[3] = 0x00;
        beginning_corrupted_bytes[4] = 0xFF;
    }
    let beginning_corrupted_result = state::SessionState::from_bytes(&beginning_corrupted_bytes);
    assert!(
        beginning_corrupted_result.is_err(),
        "Beginning-corrupted byte array should fail to deserialize"
    );

    // Test Case 5: Random byte corruption at the end
    let mut end_corrupted_bytes = valid_bytes.clone();
    let len = end_corrupted_bytes.len();
    if len > 5 {
        end_corrupted_bytes[len - 1] = 0xFF;
        end_corrupted_bytes[len - 2] = 0x00;
        end_corrupted_bytes[len - 3] = 0xFF;
        end_corrupted_bytes[len - 4] = 0x00;
        end_corrupted_bytes[len - 5] = 0xFF;
    }
    let end_corrupted_result = state::SessionState::from_bytes(&end_corrupted_bytes);
    assert!(
        end_corrupted_result.is_err(),
        "End-corrupted byte array should fail to deserialize"
    );

    // Test Case 6: Empty byte array
    let empty_bytes = &[];
    let empty_result = state::SessionState::from_bytes(empty_bytes);
    assert!(
        empty_result.is_err(),
        "Empty byte array should fail to deserialize"
    );

    // Test Case 7: Single byte
    let single_byte = &[0x42];
    let single_byte_result = state::SessionState::from_bytes(single_byte);
    assert!(
        single_byte_result.is_err(),
        "Single byte should fail to deserialize"
    );

    // Test Case 8: Invalid UTF-8 bytes (since from_bytes expects JSON internally)
    let invalid_utf8_bytes = &[0xFF, 0xFE, 0xFD, 0xFC, 0xFB, 0xFA];
    let invalid_utf8_result = state::SessionState::from_bytes(invalid_utf8_bytes);
    assert!(
        invalid_utf8_result.is_err(),
        "Invalid UTF-8 bytes should fail to deserialize"
    );

    // Test Case 9: Valid UTF-8 but not JSON
    let not_json_bytes = b"This is valid UTF-8 but not JSON";
    let not_json_result = state::SessionState::from_bytes(not_json_bytes);
    assert!(
        not_json_result.is_err(),
        "Valid UTF-8 non-JSON bytes should fail to deserialize"
    );

    // Test Case 10: Bytes that form valid JSON but wrong structure
    let wrong_structure_bytes = b"[1, 2, 3, 4, 5]";
    let wrong_structure_result = state::SessionState::from_bytes(wrong_structure_bytes);
    assert!(
        wrong_structure_result.is_err(),
        "Wrong JSON structure bytes should fail to deserialize"
    );

    // Test Case 11: Systematic corruption - flip every nth bit
    let mut systematic_corrupted_bytes = valid_bytes.clone();
    for i in (0..systematic_corrupted_bytes.len()).step_by(8) {
        if i < systematic_corrupted_bytes.len() {
            systematic_corrupted_bytes[i] ^= 0x01; // Flip the least significant bit
        }
    }
    let systematic_corrupted_result = state::SessionState::from_bytes(&systematic_corrupted_bytes);
    assert!(
        systematic_corrupted_result.is_err(),
        "Systematically corrupted byte array should fail to deserialize"
    );

    // Test Case 12: Insert extra bytes in the middle
    let mut extra_bytes = Vec::new();
    let middle = valid_bytes.len() / 2;
    extra_bytes.extend_from_slice(&valid_bytes[..middle]);
    extra_bytes.extend_from_slice(&[0xDE, 0xAD, 0xBE, 0xEF]); // Insert garbage bytes
    extra_bytes.extend_from_slice(&valid_bytes[middle..]);
    let extra_bytes_result = state::SessionState::from_bytes(&extra_bytes);
    assert!(
        extra_bytes_result.is_err(),
        "Byte array with extra bytes should fail to deserialize"
    );

    // Verify that all error cases return appropriate error types
    let test_cases = vec![
        ("truncated", truncated_result),
        ("half_truncated", half_truncated_result),
        ("bit_flipped", bit_flipped_result),
        ("beginning_corrupted", beginning_corrupted_result),
        ("end_corrupted", end_corrupted_result),
        ("empty", empty_result),
        ("single_byte", single_byte_result),
        ("invalid_utf8", invalid_utf8_result),
        ("not_json", not_json_result),
        ("wrong_structure", wrong_structure_result),
        ("systematic_corrupted", systematic_corrupted_result),
        ("extra_bytes", extra_bytes_result),
    ];

    for (test_name, result) in test_cases {
        match result {
            Err(crate::error::ProtocolError::DeserializationError { .. }) => {
                // Expected error type - test passes
            }
            Err(other_error) => {
                println!("Test case '{}' returned unexpected error type: {:?}", test_name, other_error);
                // Some error types might be acceptable depending on the specific corruption
                // The important thing is that it fails gracefully rather than panicking
            }
            Ok(_) => {
                panic!("Test case '{}' should have failed but succeeded", test_name);
            }
        }
    }

    // Additional verification: Ensure that the function doesn't panic on any input
    // Test with various edge cases that might cause panics
    let panic_test_cases = vec![
        vec![],                                    // Empty
        vec![0],                                   // Single byte
        vec![0xFF; 1000],                         // Large array of 0xFF
        vec![0x00; 1000],                         // Large array of 0x00
        (0..=255).collect::<Vec<u8>>(),           // All possible byte values
        vec![0x7F; 100],                          // Valid ASCII range
        vec![0x80; 100],                          // High bit set
    ];

    for test_input in panic_test_cases {
        let result = std::panic::catch_unwind(|| {
            state::SessionState::from_bytes(&test_input)
        });
        
        assert!(
            result.is_ok(),
            "from_bytes should not panic on input of length: {}",
            test_input.len()
        );
        
        // The function should return an error, not panic
        if let Ok(deserialization_result) = result {
            assert!(
                deserialization_result.is_err(),
                "Malformed input of length {} should result in an error, not success",
                test_input.len()
            );
        }
    }

    // Test Case 13: Verify that corrupted data doesn't produce valid-looking but incorrect sessions
    // This is important for security - we want to fail fast rather than produce garbage
    let mut subtle_corruption_bytes = valid_bytes.clone();
    if subtle_corruption_bytes.len() > 50 {
        // Make a very small change that might not be immediately obvious
        let index = subtle_corruption_bytes.len() - 20;
        subtle_corruption_bytes[index] ^= 0x01;
    }
    let subtle_corruption_result = state::SessionState::from_bytes(&subtle_corruption_bytes);
    
    // This should either fail completely or (if it somehow succeeds) produce a session
    // that's detectably different from the original
    match subtle_corruption_result {
        Err(_) => {
            // Expected - corruption should be detected
        }
        Ok(corrupted_session) => {
            // If it somehow succeeds, it should be different from the original
            let original_json = original_session.to_json().expect("Original should serialize");
            let corrupted_json = corrupted_session.to_json().expect("Corrupted should serialize");
            assert_ne!(
                original_json, corrupted_json,
                "Subtly corrupted session should be detectably different from original"
            );
        }
    }
}

/// Test for subtask 4.4: Test Decryption Failure for Message from an Unrelated Session
///
/// This test verifies that a session cannot decrypt a message that was intended for
/// a completely different session, confirming session isolation. This is critical
/// for ensuring that messages encrypted for one pair of parties cannot be decrypted
/// by any other parties, even if they have valid sessions.
#[test]
fn test_decryption_failure_for_message_from_unrelated_session() {
    // Setup: Create two completely independent sessions with different secrets and keys
    
    // Session AB: Alice and Bob
    let x3dh_secret_ab = [0x42u8; 32];
    let alice_identity = KeyPair::from_seed(&[0x01u8; 32])
        .expect("Failed to create Alice identity key");
    let alice_signed_prekey = KeyPair::from_seed(&[0x02u8; 32])
        .expect("Failed to create Alice signed prekey");
    let bob_identity = KeyPair::from_seed(&[0x03u8; 32])
        .expect("Failed to create Bob identity key");

    // Session CD: Charlie and David
    let x3dh_secret_cd = [0x99u8; 32]; // Different secret
    let charlie_identity = KeyPair::from_seed(&[0x11u8; 32])
        .expect("Failed to create Charlie identity key");
    let charlie_signed_prekey = KeyPair::from_seed(&[0x12u8; 32])
        .expect("Failed to create Charlie signed prekey");
    let david_identity = KeyPair::from_seed(&[0x13u8; 32])
        .expect("Failed to create David identity key");

    // Create Session AB (Alice as initiator, Bob as responder)
    let alice_bundle = PreKeyBundle {
        identity_key: alice_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: alice_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut alice_session = state::SessionState::new_initiator(
        x3dh_secret_ab,
        &alice_bundle,
        1000, // max_skip
        Some("session-ab-alice".to_string()),
    )
    .expect("Failed to create Alice session");

    let mut bob_session = state::SessionState::new_responder(
        x3dh_secret_ab,
        alice_signed_prekey.clone(),
        bob_identity.public_key(),
        1000, // max_skip
        Some("session-ab-bob".to_string()),
    )
    .expect("Failed to create Bob session");

    // Create Session CD (Charlie as initiator, David as responder)
    let charlie_bundle = PreKeyBundle {
        identity_key: charlie_identity.public_key(),
        identity_verifying_key: vec![0u8; 32],
        signed_prekey: charlie_signed_prekey.public_key(),
        signed_prekey_signature: vec![0u8; 64],
        onetime_prekeys: vec![],
        timestamp: None,
    };

    let mut charlie_session = state::SessionState::new_initiator(
        x3dh_secret_cd,
        &charlie_bundle,
        1000, // max_skip
        Some("session-cd-charlie".to_string()),
    )
    .expect("Failed to create Charlie session");

    let mut david_session = state::SessionState::new_responder(
        x3dh_secret_cd,
        charlie_signed_prekey.clone(),
        david_identity.public_key(),
        1000, // max_skip
        Some("session-cd-david".to_string()),
    )
    .expect("Failed to create David session");

    // Step 1: Establish both sessions independently
    
    // Establish Session AB
    let alice_initial_message = b"Hello Bob from Alice";
    let (alice_initial_header, alice_initial_ciphertext) = alice_session.encrypt(alice_initial_message)
        .expect("Alice initial message encryption should succeed");
    
    let alice_initial_decrypted = bob_session.decrypt(&alice_initial_header, &alice_initial_ciphertext)
        .expect("Bob should decrypt Alice's initial message");
    assert_eq!(alice_initial_decrypted, alice_initial_message);

    // Establish Session CD
    let charlie_initial_message = b"Hello David from Charlie";
    let (charlie_initial_header, charlie_initial_ciphertext) = charlie_session.encrypt(charlie_initial_message)
        .expect("Charlie initial message encryption should succeed");
    
    let charlie_initial_decrypted = david_session.decrypt(&charlie_initial_header, &charlie_initial_ciphertext)
        .expect("David should decrypt Charlie's initial message");
    assert_eq!(charlie_initial_decrypted, charlie_initial_message);

    // Step 2: Verify that both sessions work correctly within their own context
    
    // Test Session AB bidirectional communication
    let bob_reply = b"Hello Alice from Bob";
    let (bob_reply_header, bob_reply_ciphertext) = bob_session.encrypt(bob_reply)
        .expect("Bob reply encryption should succeed");
    
    let bob_reply_decrypted = alice_session.decrypt(&bob_reply_header, &bob_reply_ciphertext)
        .expect("Alice should decrypt Bob's reply");
    assert_eq!(bob_reply_decrypted, bob_reply);

    // Test Session CD bidirectional communication
    let david_reply = b"Hello Charlie from David";
    let (david_reply_header, david_reply_ciphertext) = david_session.encrypt(david_reply)
        .expect("David reply encryption should succeed");
    
    let david_reply_decrypted = charlie_session.decrypt(&david_reply_header, &david_reply_ciphertext)
        .expect("Charlie should decrypt David's reply");
    assert_eq!(david_reply_decrypted, david_reply);

    // Step 3: Test cross-session decryption failures
    
    // Test 3a: Try to decrypt Charlie's message using Alice's session
    let charlie_secret_message = b"Secret message from Charlie to David";
    let (charlie_secret_header, charlie_secret_ciphertext) = charlie_session.encrypt(charlie_secret_message)
        .expect("Charlie secret message encryption should succeed");

    // Verify David can decrypt it (sanity check)
    let charlie_secret_decrypted = david_session.decrypt(&charlie_secret_header, &charlie_secret_ciphertext)
        .expect("David should decrypt Charlie's secret message");
    assert_eq!(charlie_secret_decrypted, charlie_secret_message);

    // Try to decrypt with Alice's session (should fail)
    let mut alice_session_copy = alice_session.clone();
    let alice_cross_decrypt_result = alice_session_copy.decrypt(&charlie_secret_header, &charlie_secret_ciphertext);
    assert!(
        alice_cross_decrypt_result.is_err(),
        "Alice should not be able to decrypt Charlie's message to David"
    );

    // Verify the error type
    match alice_cross_decrypt_result.unwrap_err() {
        crate::error::ProtocolError::DecryptionFailure { reason } => {
            assert!(
                reason.contains("Failed to decrypt message"),
                "Error should indicate decryption failure, got: {}",
                reason
            );
        }
        crate::error::ProtocolError::InvalidMessageFormat { details } => {
            // This is also acceptable as the message format may be invalid for this session
            assert!(
                details.contains("key") || details.contains("message"),
                "Error should indicate invalid message format, got: {}",
                details
            );
        }
        other_error => {
            panic!("Expected DecryptionFailure or InvalidMessageFormat error, got: {:?}", other_error);
        }
    }

    // Try to decrypt with Bob's session (should also fail)
    let mut bob_session_copy = bob_session.clone();
    let bob_cross_decrypt_result = bob_session_copy.decrypt(&charlie_secret_header, &charlie_secret_ciphertext);
    assert!(
        bob_cross_decrypt_result.is_err(),
        "Bob should not be able to decrypt Charlie's message to David"
    );

    // Test 3b: Try to decrypt Alice's message using Charlie's session
    let alice_secret_message = b"Secret message from Alice to Bob";
    let (alice_secret_header, alice_secret_ciphertext) = alice_session.encrypt(alice_secret_message)
        .expect("Alice secret message encryption should succeed");

    // Verify Bob can decrypt it (sanity check)
    let alice_secret_decrypted = bob_session.decrypt(&alice_secret_header, &alice_secret_ciphertext)
        .expect("Bob should decrypt Alice's secret message");
    assert_eq!(alice_secret_decrypted, alice_secret_message);

    // Try to decrypt with Charlie's session (should fail)
    let mut charlie_session_copy = charlie_session.clone();
    let charlie_cross_decrypt_result = charlie_session_copy.decrypt(&alice_secret_header, &alice_secret_ciphertext);
    assert!(
        charlie_cross_decrypt_result.is_err(),
        "Charlie should not be able to decrypt Alice's message to Bob"
    );

    // Try to decrypt with David's session (should also fail)
    let mut david_session_copy = david_session.clone();
    let david_cross_decrypt_result = david_session_copy.decrypt(&alice_secret_header, &alice_secret_ciphertext);
    assert!(
        david_cross_decrypt_result.is_err(),
        "David should not be able to decrypt Alice's message to Bob"
    );

    // Step 4: Test with messages that have similar structure but different cryptographic material
    
    // Create messages with the same plaintext but from different sessions
    let common_plaintext = b"This is the same message content";
    
    let (alice_common_header, alice_common_ciphertext) = alice_session.encrypt(common_plaintext)
        .expect("Alice common message encryption should succeed");
    
    let (charlie_common_header, charlie_common_ciphertext) = charlie_session.encrypt(common_plaintext)
        .expect("Charlie common message encryption should succeed");

    // Verify each session can decrypt its own message
    let alice_common_decrypted = bob_session.decrypt(&alice_common_header, &alice_common_ciphertext)
        .expect("Bob should decrypt Alice's common message");
    assert_eq!(alice_common_decrypted, common_plaintext);

    let charlie_common_decrypted = david_session.decrypt(&charlie_common_header, &charlie_common_ciphertext)
        .expect("David should decrypt Charlie's common message");
    assert_eq!(charlie_common_decrypted, common_plaintext);

    // Try cross-session decryption (should fail even with same plaintext)
    let mut bob_session_copy2 = bob_session.clone();
    let bob_cross_common_result = bob_session_copy2.decrypt(&charlie_common_header, &charlie_common_ciphertext);
    assert!(
        bob_cross_common_result.is_err(),
        "Bob should not be able to decrypt Charlie's message even with same plaintext"
    );

    let mut david_session_copy2 = david_session.clone();
    let david_cross_common_result = david_session_copy2.decrypt(&alice_common_header, &alice_common_ciphertext);
    assert!(
        david_cross_common_result.is_err(),
        "David should not be able to decrypt Alice's message even with same plaintext"
    );

    // Step 5: Verify that session isolation doesn't affect legitimate functionality
    
    // Both sessions should continue to work normally
    let alice_final_message = b"Final message from Alice";
    let (alice_final_header, alice_final_ciphertext) = alice_session.encrypt(alice_final_message)
        .expect("Alice final message encryption should succeed");
    
    let alice_final_decrypted = bob_session.decrypt(&alice_final_header, &alice_final_ciphertext)
        .expect("Bob should decrypt Alice's final message");
    assert_eq!(alice_final_decrypted, alice_final_message);

    let charlie_final_message = b"Final message from Charlie";
    let (charlie_final_header, charlie_final_ciphertext) = charlie_session.encrypt(charlie_final_message)
        .expect("Charlie final message encryption should succeed");
    
    let charlie_final_decrypted = david_session.decrypt(&charlie_final_header, &charlie_final_ciphertext)
        .expect("David should decrypt Charlie's final message");
    assert_eq!(charlie_final_decrypted, charlie_final_message);

    // Step 6: Verify session health and state integrity
    
    // All sessions should be healthy and functional
    assert!(alice_session.can_send(), "Alice session should be able to send");
    assert!(alice_session.can_receive(), "Alice session should be able to receive");
    assert!(bob_session.can_send(), "Bob session should be able to send");
    assert!(bob_session.can_receive(), "Bob session should be able to receive");
    assert!(charlie_session.can_send(), "Charlie session should be able to send");
    assert!(charlie_session.can_receive(), "Charlie session should be able to receive");
    assert!(david_session.can_send(), "David session should be able to send");
    assert!(david_session.can_receive(), "David session should be able to receive");

    // Session counters should have advanced independently
    assert!(alice_session.sending_message_number > 0, "Alice should have sent messages");
    assert!(bob_session.sending_message_number >= 0, "Bob session should be valid");
    assert!(charlie_session.sending_message_number > 0, "Charlie should have sent messages");
    assert!(david_session.sending_message_number >= 0, "David session should be valid");
    
    // Verify that receiving counters have advanced
    assert!(alice_session.receiving_message_number > 0, "Alice should have received messages");
    assert!(bob_session.receiving_message_number > 0, "Bob should have received messages");
    assert!(charlie_session.receiving_message_number > 0, "Charlie should have received messages");
    assert!(david_session.receiving_message_number > 0, "David should have received messages");
}
