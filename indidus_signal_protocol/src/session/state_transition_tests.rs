#[cfg(test)]
mod tests {
    use crate::session::SessionState;
    use crate::crypto::keys::KeyPair;
    use crate::crypto::bundle::PreKeyBundle;

    /// Test fixture containing deterministic keys for reproducible testing
    #[allow(dead_code)]
    #[derive(Debug, <PERSON>lone)]
    pub struct TestKeys {
        // Alice (initiator) keys
        pub alice_identity: KeyPair,
        pub alice_dh_ratchet: KeyPair,
        
        // <PERSON> (responder) keys  
        pub bob_identity: KeyPair,
        pub bob_signed_prekey: KeyPair,
        pub bob_onetime_prekey: KeyPair,
        
        // Shared secret for deterministic testing
        pub shared_secret: [u8; 32],
    }

    impl TestKeys {
        /// Create deterministic test keys using fixed seeds
        /// This ensures all tests are reproducible and deterministic
        pub fn new_deterministic() -> Self {
            // Use fixed seeds for deterministic key generation
            let alice_identity_seed = [1u8; 32];
            let alice_dh_seed = [2u8; 32];
            let bob_identity_seed = [3u8; 32];
            let bob_signed_prekey_seed = [4u8; 32];
            let bob_onetime_prekey_seed = [5u8; 32];
            let shared_secret = [42u8; 32]; // Fixed shared secret for testing

            Self {
                alice_identity: KeyPair::from_seed(&alice_identity_seed).expect("Failed to generate Alice identity key"),
                alice_dh_ratchet: KeyPair::from_seed(&alice_dh_seed).expect("Failed to generate Alice DH key"),
                bob_identity: KeyPair::from_seed(&bob_identity_seed).expect("Failed to generate Bob identity key"),
                bob_signed_prekey: KeyPair::from_seed(&bob_signed_prekey_seed).expect("Failed to generate Bob signed prekey"),
                bob_onetime_prekey: KeyPair::from_seed(&bob_onetime_prekey_seed).expect("Failed to generate Bob onetime prekey"),
                shared_secret,
            }
        }

        /// Create Bob's pre-key bundle for X3DH
        pub fn bob_prekey_bundle(&self) -> PreKeyBundle {
            // For testing, create a dummy signature and verifying key
            let dummy_signature = crate::crypto::signature::Ed25519Signature::from_bytes([0u8; 64]);
            let dummy_verifying_key = vec![0u8; 32]; // Dummy Ed25519 verifying key
            
            let onetime_prekeys = vec![self.bob_onetime_prekey.public_key()];
            
            PreKeyBundle::new(
                self.bob_identity.public_key(),
                dummy_verifying_key,
                self.bob_signed_prekey.public_key(),
                dummy_signature,
                onetime_prekeys,
            )
        }
    }

    /// Test session pair containing Alice (initiator) and Bob (responder) sessions
    #[allow(dead_code)]
    #[derive(Debug)]
    pub struct TestSessionPair {
        pub alice: SessionState,
        pub bob: SessionState,
        pub keys: TestKeys,
    }

    #[allow(dead_code)]
    impl TestSessionPair {
        /// Setup deterministic test sessions for Alice (initiator) and Bob (responder)
        /// 
        /// This function creates two session states ready for message exchange:
        /// - Alice: Configured as initiator, can immediately send messages
        /// - Bob: Configured as responder, ready to receive first message
        /// 
        /// All keys are deterministic to ensure reproducible test results.
        pub fn setup_test_sessions() -> Self {
            let keys = TestKeys::new_deterministic();
            
            // Create Alice's session as initiator
            let alice = SessionState::new_initiator(
                keys.shared_secret,
                &keys.bob_prekey_bundle(),
                1000, // max_skip
                Some("test-alice-session".to_string()),
            ).expect("Failed to create Alice's session");

            // Create Bob's session as responder  
            let bob = SessionState::new_responder(
                keys.shared_secret,
                keys.bob_signed_prekey.clone(),
                keys.alice_identity.public_key(),
                1000, // max_skip
                Some("test-bob-session".to_string()),
            ).expect("Failed to create Bob's session");

            Self { alice, bob, keys }
        }

        /// Setup sessions with custom parameters for advanced testing
        pub fn setup_custom_sessions(
            max_skip: u32,
            alice_session_id: Option<String>,
            bob_session_id: Option<String>,
        ) -> Self {
            let keys = TestKeys::new_deterministic();
            
            let alice = SessionState::new_initiator(
                keys.shared_secret,
                &keys.bob_prekey_bundle(),
                max_skip,
                alice_session_id,
            ).expect("Failed to create Alice's session");

            let bob = SessionState::new_responder(
                keys.shared_secret,
                keys.bob_signed_prekey.clone(),
                keys.alice_identity.public_key(),
                max_skip,
                bob_session_id,
            ).expect("Failed to create Bob's session");

            Self { alice, bob, keys }
        }

        /// Create sessions from a shared secret for simplified testing
        pub fn from_shared_secret(
            shared_secret: [u8; 32],
            alice_is_initiator: bool,
        ) -> Self {
            let keys = TestKeys::new_deterministic();
            
            let alice = SessionState::from_shared_secret(
                shared_secret,
                keys.alice_dh_ratchet.clone(),
                if alice_is_initiator { Some(keys.bob_signed_prekey.public_key()) } else { None },
                1000,
                alice_is_initiator,
            ).expect("Failed to create Alice's session");

            let bob = SessionState::from_shared_secret(
                shared_secret,
                keys.bob_signed_prekey.clone(),
                if !alice_is_initiator { Some(keys.alice_dh_ratchet.public_key()) } else { None },
                1000,
                !alice_is_initiator,
            ).expect("Failed to create Bob's session");

            Self { alice, bob, keys }
        }
    }

    /// Helper functions for state verification
    impl TestSessionPair {
        /// Verify that Alice's session is properly configured as initiator
        pub fn verify_alice_initiator_state(&self) {
            assert!(self.alice.can_send(), "Alice should be able to send messages");
            assert_eq!(self.alice.sending_message_number, 0, "Alice should start with message number 0");
            assert_eq!(self.alice.receiving_message_number, 0, "Alice should start with receiving number 0");
            assert!(self.alice.sending_chain_key.is_some(), "Alice should have a sending chain key");
            assert!(self.alice.receiving_chain_key.is_none(), "Alice should not have a receiving chain key initially");
        }

        /// Verify that Bob's session is properly configured as responder
        pub fn verify_bob_responder_state(&self) {
            assert!(!self.bob.can_send(), "Bob should not be able to send messages initially");
            assert_eq!(self.bob.sending_message_number, 0, "Bob should start with message number 0");
            assert_eq!(self.bob.receiving_message_number, 0, "Bob should start with receiving number 0");
            assert!(self.bob.sending_chain_key.is_none(), "Bob should not have a sending chain key initially");
            assert!(self.bob.receiving_chain_key.is_none(), "Bob should not have a receiving chain key initially");
        }

        /// Verify session metadata is correctly set
        pub fn verify_session_metadata(&self) {
            // Check Alice's metadata
            assert_eq!(self.alice.session_id, Some("test-alice-session".to_string()));
            assert!(self.alice.created_at.is_some(), "Alice session should have creation timestamp");
            assert!(self.alice.last_activity.is_some(), "Alice session should have last activity timestamp");
            assert_eq!(self.alice.max_skip, 1000, "Alice should have correct max_skip");

            // Check Bob's metadata
            assert_eq!(self.bob.session_id, Some("test-bob-session".to_string()));
            assert!(self.bob.created_at.is_some(), "Bob session should have creation timestamp");
            assert!(self.bob.last_activity.is_some(), "Bob session should have last activity timestamp");
            assert_eq!(self.bob.max_skip, 1000, "Bob should have correct max_skip");
        }

        /// Verify that both sessions have the same root key (from shared secret)
        #[allow(dead_code)]
        pub fn verify_shared_root_key(&self) {
            // Note: After DH ratchet in initiator setup, root keys may differ
            // This test is more relevant for the initial state before any ratchet steps
            assert_eq!(self.alice.max_skip, self.bob.max_skip, "Both sessions should have same max_skip");
        }

        /// Get a snapshot of current session state for comparison
        pub fn get_state_snapshot(&self) -> SessionStateSnapshot {
            SessionStateSnapshot {
                alice_sending_msg_num: self.alice.sending_message_number,
                alice_receiving_msg_num: self.alice.receiving_message_number,
                alice_can_send: self.alice.can_send(),
                alice_can_receive: self.alice.can_receive(),
                alice_skipped_keys: self.alice.skipped_keys_count(),
                
                bob_sending_msg_num: self.bob.sending_message_number,
                bob_receiving_msg_num: self.bob.receiving_message_number,
                bob_can_send: self.bob.can_send(),
                bob_can_receive: self.bob.can_receive(),
                bob_skipped_keys: self.bob.skipped_keys_count(),
            }
        }
    }

    /// Snapshot of session state for before/after comparisons
    #[derive(Debug, Clone, PartialEq)]
    pub struct SessionStateSnapshot {
        pub alice_sending_msg_num: u32,
        pub alice_receiving_msg_num: u32,
        pub alice_can_send: bool,
        pub alice_can_receive: bool,
        pub alice_skipped_keys: usize,
        
        pub bob_sending_msg_num: u32,
        pub bob_receiving_msg_num: u32,
        pub bob_can_send: bool,
        pub bob_can_receive: bool,
        pub bob_skipped_keys: usize,
    }

    /// Test message content for deterministic testing
    #[allow(dead_code)]
    pub const TEST_MESSAGE_1: &[u8] = b"Hello from Alice!";
    #[allow(dead_code)]
    pub const TEST_MESSAGE_2: &[u8] = b"Hello from Bob!";
    #[allow(dead_code)]
    pub const TEST_MESSAGE_3: &[u8] = b"Second message from Alice";
    #[allow(dead_code)]
    pub const TEST_MESSAGE_EMPTY: &[u8] = b"";

    // ============================================================================
    // SUBTASK 2.1: Test Setup Infrastructure
    // ============================================================================

    #[test]
    fn test_deterministic_key_generation() {
        // Generate keys multiple times and verify they're identical
        let keys1 = TestKeys::new_deterministic();
        let keys2 = TestKeys::new_deterministic();

        // Verify all keys are deterministic
        assert_eq!(keys1.alice_identity.public_key().as_bytes(), 
                   keys2.alice_identity.public_key().as_bytes(),
                   "Alice identity keys should be deterministic");
        
        assert_eq!(keys1.bob_identity.public_key().as_bytes(),
                   keys2.bob_identity.public_key().as_bytes(),
                   "Bob identity keys should be deterministic");
        
        assert_eq!(keys1.shared_secret, keys2.shared_secret,
                   "Shared secrets should be deterministic");
    }

    #[test]
    fn test_session_pair_setup() {
        let sessions = TestSessionPair::setup_test_sessions();
        
        // Verify basic setup
        sessions.verify_alice_initiator_state();
        sessions.verify_bob_responder_state();
        sessions.verify_session_metadata();
    }

    #[test]
    fn test_custom_session_setup() {
        let sessions = TestSessionPair::setup_custom_sessions(
            500, // custom max_skip
            Some("custom-alice".to_string()),
            Some("custom-bob".to_string()),
        );

        assert_eq!(sessions.alice.max_skip, 500);
        assert_eq!(sessions.bob.max_skip, 500);
        assert_eq!(sessions.alice.session_id, Some("custom-alice".to_string()));
        assert_eq!(sessions.bob.session_id, Some("custom-bob".to_string()));
    }

    #[test]
    fn test_session_health_info() {
        let sessions = TestSessionPair::setup_test_sessions();
        
        let alice_health = sessions.alice.health_info();
        let bob_health = sessions.bob.health_info();

        // Verify Alice's health (initiator)
        assert!(alice_health.can_send, "Alice should be able to send");
        assert!(!alice_health.can_receive, "Alice should not be able to receive initially");
        assert_eq!(alice_health.skipped_keys, 0, "Alice should have no skipped keys");
        assert!(!alice_health.near_skip_limit, "Alice should not be near skip limit");

        // Verify Bob's health (responder)
        assert!(!bob_health.can_send, "Bob should not be able to send initially");
        assert!(!bob_health.can_receive, "Bob should not be able to receive initially");
        assert_eq!(bob_health.skipped_keys, 0, "Bob should have no skipped keys");
        assert!(!bob_health.near_skip_limit, "Bob should not be near skip limit");
    }

    #[test]
    fn test_state_snapshot_functionality() {
        let sessions = TestSessionPair::setup_test_sessions();
        let snapshot1 = sessions.get_state_snapshot();
        let snapshot2 = sessions.get_state_snapshot();

        // Snapshots should be identical for unchanged sessions
        assert_eq!(snapshot1, snapshot2, "Identical sessions should produce identical snapshots");

        // Verify snapshot content
        assert_eq!(snapshot1.alice_sending_msg_num, 0);
        assert_eq!(snapshot1.alice_receiving_msg_num, 0);
        assert!(snapshot1.alice_can_send);
        assert!(!snapshot1.alice_can_receive);
        
        assert_eq!(snapshot1.bob_sending_msg_num, 0);
        assert_eq!(snapshot1.bob_receiving_msg_num, 0);
        assert!(!snapshot1.bob_can_send);
        assert!(!snapshot1.bob_can_receive);
    }

    #[test]
    fn test_prekey_bundle_creation() {
        let keys = TestKeys::new_deterministic();
        let bundle = keys.bob_prekey_bundle();

        assert_eq!(bundle.identity_key(), keys.bob_identity.public_key());
        assert_eq!(bundle.signed_prekey(), keys.bob_signed_prekey.public_key());
        assert!(!bundle.onetime_prekeys().is_empty(), "Bundle should have one-time prekeys");
    }

    #[test]
    fn test_session_serialization_roundtrip() {
        let sessions = TestSessionPair::setup_test_sessions();
        
        // Test Alice's session serialization
        let alice_json = sessions.alice.to_json().expect("Failed to serialize Alice's session");
        let alice_restored = SessionState::from_json(&alice_json).expect("Failed to deserialize Alice's session");
        
        // Verify key fields are preserved
        assert_eq!(sessions.alice.root_key, alice_restored.root_key);
        assert_eq!(sessions.alice.sending_message_number, alice_restored.sending_message_number);
        assert_eq!(sessions.alice.receiving_message_number, alice_restored.receiving_message_number);
        assert_eq!(sessions.alice.max_skip, alice_restored.max_skip);
        assert_eq!(sessions.alice.session_id, alice_restored.session_id);

        // Test Bob's session serialization
        let bob_json = sessions.bob.to_json().expect("Failed to serialize Bob's session");
        let bob_restored = SessionState::from_json(&bob_json).expect("Failed to deserialize Bob's session");
        
        assert_eq!(sessions.bob.root_key, bob_restored.root_key);
        assert_eq!(sessions.bob.sending_message_number, bob_restored.sending_message_number);
        assert_eq!(sessions.bob.receiving_message_number, bob_restored.receiving_message_number);
        assert_eq!(sessions.bob.max_skip, bob_restored.max_skip);
        assert_eq!(sessions.bob.session_id, bob_restored.session_id);
    }
}