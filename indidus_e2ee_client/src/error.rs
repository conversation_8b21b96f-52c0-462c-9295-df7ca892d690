//! Error handling for the Indidus E2EE Client SDK
//!
//! This module provides comprehensive error handling for all client operations,
//! with clear categorization and actionable error messages for developers.

use indidus_shared::validation::PeerId;
use indidus_signal_protocol::ProtocolError;
use thiserror::Error;
use uuid::Uuid;

/// Primary error type for the Indidus E2EE Client SDK
///
/// This enum provides comprehensive error handling for all client operations,
/// categorizing errors by their source and providing actionable guidance for developers.
/// All error messages include context and suggestions for resolution.
///
/// # Error Categories
///
/// ## Protocol and Cryptographic Errors
/// - [`Protocol`](ClientError::Protocol): Errors from the underlying Signal Protocol
/// - [`Session`](ClientError::Session): Session management failures
/// - [`KeyManagement`](ClientError::KeyManagement): Key generation and storage issues
///
/// ## Network and Communication Errors
/// - [`Network`](ClientError::Network): HTTP/HTTPS connection problems
/// - [`WebSocket`](ClientError::WebSocket): Real-time connection issues
/// - [`InvalidEventPayload`](ClientError::InvalidEventPayload): Malformed server messages
///
/// ## State Management Errors
/// - [`StateSaveFailed`](ClientError::StateSaveFailed): Client state serialization failures
/// - [`StateCorrupted`](ClientError::StateCorrupted): Invalid or corrupted saved state
/// - [`StateIncompatible`](ClientError::StateIncompatible): Version compatibility issues
///
/// ## Configuration and Initialization Errors
/// - [`InvalidConfiguration`](ClientError::InvalidConfiguration): Invalid client settings
/// - [`NotInitialized`](ClientError::NotInitialized): Operations on uninitialized client
/// - [`Internal`](ClientError::Internal): Unexpected internal failures
///
/// # Usage Examples
///
/// ```rust,no_run
/// use indidus_e2ee_client::{Client, ClientConfig, ClientError};
/// use url::Url;
///
/// # async fn example() -> Result<(), ClientError> {
/// let config = ClientConfig::new(Url::parse("https://server.com")?);
/// let client = Client::new(config)?;
///
/// // Handle specific error types
/// match client.save_state("client_state.json").await {
///     Ok(()) => println!("State saved successfully"),
///     Err(ClientError::NotInitialized { .. }) => {
///         println!("Client needs to be initialized first");
///     },
///     Err(ClientError::StateSaveFailed { details }) => {
///         println!("Failed to save state: {}", details);
///     },
///     Err(e) => println!("Other error: {}", e),
/// }
/// # Ok(())
/// # }
/// ```
#[derive(Error, Debug)]
pub enum ClientError {
    // === Protocol and Cryptographic Errors ===
    /// Error from the underlying Signal Protocol implementation
    ///
    /// This error occurs when cryptographic operations fail, such as:
    /// - Message encryption/decryption failures
    /// - Invalid key material or session state
    /// - Protocol version mismatches
    /// - Signature verification failures
    ///
    /// **Resolution**: Check that sessions are properly established and that
    /// both parties are using compatible protocol versions.
    #[error("Protocol error: {0}")]
    Protocol(#[from] ProtocolError),

    /// Session management errors
    ///
    /// Occurs when session operations fail, such as:
    /// - Session initialization with invalid parameters
    /// - Attempting to use a non-existent session
    /// - Session state corruption or inconsistency
    ///
    /// **Resolution**: Re-establish the session with the remote client or verify
    /// that the client ID is correct and the session was properly initialized.
    #[error("Session error for client {client_id}: {details}. Try re-establishing the session or checking the client ID.")]
    Session { client_id: String, details: String },

    /// Key management errors
    ///
    /// Occurs during cryptographic key operations, such as:
    /// - Key generation failures due to insufficient entropy
    /// - Key storage or retrieval errors
    /// - Invalid key formats or corrupted key material
    ///
    /// **Resolution**: Ensure the system has sufficient entropy for key generation
    /// and that key storage mechanisms are properly configured.
    #[error("Key management error: {operation} failed - {reason}. Ensure proper key initialization and storage.")]
    KeyManagement { operation: String, reason: String },

    /// Cryptographic operation errors
    ///
    /// Occurs during cryptographic operations, such as:
    /// - X3DH handshake failures
    /// - Key derivation errors
    /// - Signature verification failures
    /// - Protocol-level cryptographic errors
    ///
    /// **Resolution**: Ensure keys are properly generated and that both parties
    /// are using compatible cryptographic parameters.
    #[error("Cryptographic error: {operation} failed - {details}")]
    CryptoError { operation: String, details: String },

    // === Network and Communication Errors ===
    /// HTTP/HTTPS network errors
    ///
    /// Occurs during HTTP requests to the server, such as:
    /// - Connection timeouts or refused connections
    /// - DNS resolution failures
    /// - TLS/SSL certificate validation errors
    /// - Server returning HTTP error status codes
    ///
    /// **Resolution**: Check internet connectivity, server availability, and
    /// ensure the server URL is correct and accessible.
    #[error("Network error: {0}. Check your internet connection and server availability.")]
    Network(#[from] reqwest::Error),

    /// WebSocket connection errors with context
    ///
    /// Enhanced WebSocket errors that include context about where the error occurred.
    /// This provides more detailed information than the basic WebSocket error.
    ///
    /// **Resolution**: Check network connectivity and server availability.
    /// If the error persists, the server may be experiencing issues.
    #[error("WebSocket error in {context}: {source}. Check your network connection and server availability.")]
    WebSocketWithContext {
        context: String,
        #[source]
        source: tokio_tungstenite::tungstenite::Error,
    },

    /// WebSocket connection errors (legacy, for backward compatibility)
    ///
    /// Occurs during real-time WebSocket communication, such as:
    /// - Connection establishment failures
    /// - Unexpected connection closures
    /// - Protocol errors or message format issues
    ///
    /// **Resolution**: Check network stability and server availability.
    /// Consider implementing connection retry logic for transient failures.
    #[error("WebSocket error: {0}. The real-time connection may be unstable.")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    /// Invalid event payload received from server
    ///
    /// Occurs when the server sends malformed or unexpected data, such as:
    /// - Invalid JSON syntax in server messages
    /// - Missing required fields in event payloads
    /// - Data that doesn't conform to the expected schema
    ///
    /// **Resolution**: This usually indicates a server-side issue or protocol
    /// version mismatch. Contact the server administrator or check for client updates.
    #[error("Invalid event payload: {details}. The server sent malformed data that could not be parsed.")]
    InvalidEventPayload { details: String },

    // === State Management Errors ===
    /// State serialization failed during save operation
    ///
    /// Occurs when saving client state to persistent storage fails, such as:
    /// - Insufficient disk space for state files
    /// - Permission errors when writing to storage
    /// - Serialization errors due to corrupted in-memory state
    ///
    /// **Resolution**: Check available disk space, file permissions, and ensure
    /// the storage location is writable.
    #[error("Failed to save client state: {details}. This may indicate insufficient disk space or permissions issues.")]
    StateSaveFailed { details: String },

    /// State deserialization failed during load operation
    ///
    /// Occurs when loading previously saved client state fails, such as:
    /// - Corrupted state files
    /// - State files from incompatible client versions
    /// - Invalid JSON or binary format in state data
    ///
    /// **Resolution**: If the state file is corrupted, you may need to delete it
    /// and reinitialize the client. Ensure you're using a compatible client version.
    #[error("Failed to load client state: {details}. The saved state may be corrupted or from an incompatible version.")]
    StateCorrupted { details: String },

    /// State validation failed during load operation
    ///
    /// Occurs when saved state is incompatible with current configuration, such as:
    /// - Client ID mismatch between saved state and configuration
    /// - Configuration parameters that conflict with saved state
    /// - Version incompatibilities that prevent state restoration
    ///
    /// **Resolution**: Ensure the client configuration matches the saved state,
    /// or create a new client instance if the configuration has changed significantly.
    #[error("State validation failed: {details}. The saved state is incompatible with the current configuration.")]
    StateIncompatible { details: String },

    /// Server communication errors
    #[error("Server error {status_code}: {message}. Contact server administrator if the issue persists.")]
    Server { status_code: u16, message: String },

    /// Connection timeout
    #[error("Connection timeout after {timeout_seconds}s. Try increasing the timeout or check network stability.")]
    ConnectionTimeout { timeout_seconds: u64 },

    // === Configuration and Initialization Errors ===
    /// Invalid client configuration
    #[error("Invalid configuration: {parameter} = '{value}'. {suggestion}")]
    InvalidConfiguration {
        parameter: String,
        value: String,
        suggestion: String,
    },

    /// Client not properly initialized
    #[error("Client not initialized: {operation} requires initialization. Call client.initialize() first.")]
    NotInitialized { operation: String },

    /// Invalid server URL
    #[error("Invalid server URL '{url}': {reason}. Ensure the URL is properly formatted and accessible.")]
    InvalidServerUrl { url: String, reason: String },

    // === File Transfer Errors ===
    /// File I/O operations failed
    #[error("File I/O error: {operation} failed for '{path}' - {reason}")]
    FileIo {
        operation: String,
        path: String,
        reason: String,
    },

    /// File transfer operation failed
    #[error("File transfer failed for transfer {transfer_id}: {reason}. Check file permissions and network connectivity.")]
    FileTransfer { transfer_id: Uuid, reason: String },

    /// File not found or inaccessible
    #[error("File not found: '{path}'. Verify the file exists and is accessible.")]
    FileNotFound { path: String },

    /// File too large for transfer
    #[error("File too large: {size_mb}MB exceeds maximum allowed size of {max_size_mb}MB. Consider splitting the file or using a different transfer method.")]
    FileTooLarge { size_mb: u64, max_size_mb: u64 },

    // === Message and Data Errors ===
    /// Message encryption failed
    #[error("Message encryption failed for recipient {recipient_id}: {reason}. Ensure a valid session exists.")]
    EncryptionFailed {
        recipient_id: PeerId,
        reason: String,
    },

    /// Message decryption failed
    #[error("Message decryption failed from sender {sender_id}: {reason}. The message may be corrupted or from an invalid session.")]
    DecryptionFailed { sender_id: PeerId, reason: String },

    /// Invalid message format
    #[error("Invalid message format: {details}. The message may be corrupted or from an incompatible client version.")]
    InvalidMessageFormat { details: String },

    /// Message too large
    #[error("Message too large: {size_kb}KB exceeds maximum allowed size of {max_size_kb}KB. Consider splitting the message or using file transfer.")]
    MessageTooLarge { size_kb: u64, max_size_kb: u64 },

    // === Serialization and Data Format Errors ===
    /// JSON serialization/deserialization failed
    #[error("JSON error: {operation} failed - {reason}. The data may be corrupted or in an unexpected format.")]
    Json { operation: String, reason: String },

    /// Data serialization failed
    #[error("Serialization error: failed to serialize {data_type} - {reason}")]
    Serialization { data_type: String, reason: String },

    /// Data deserialization failed
    #[error("Deserialization error: failed to deserialize {data_type} - {reason}. The data may be from an incompatible version.")]
    Deserialization { data_type: String, reason: String },

    // === Authentication and Authorization Errors ===
    /// Authentication failed
    #[error("Authentication failed: {reason}. Check your credentials and try again.")]
    AuthenticationFailed { reason: String },

    /// Authorization denied
    #[error("Authorization denied for operation '{operation}': {reason}. You may not have sufficient permissions.")]
    AuthorizationDenied { operation: String, reason: String },

    /// Invalid client credentials
    #[error("Invalid credentials: {details}. Ensure your client is properly registered and authenticated.")]
    InvalidCredentials { details: String },

    // === State and Lifecycle Errors ===
    /// Invalid client state for operation
    #[error("Invalid state: cannot perform '{operation}' in current state '{current_state}'. Expected state: {expected_state}.")]
    InvalidState {
        operation: String,
        current_state: String,
        expected_state: String,
    },

    /// Resource not found
    #[error("Resource not found: {resource_type} with ID '{resource_id}' does not exist. It may have been deleted or never created.")]
    ResourceNotFound {
        resource_type: String,
        resource_id: String,
    },

    /// Operation timeout
    #[error("Operation timeout: '{operation}' did not complete within {timeout_seconds}s. Try increasing the timeout or check system performance.")]
    OperationTimeout {
        operation: String,
        timeout_seconds: u64,
    },

    // === Validation and Input Errors ===
    /// Invalid input parameters
    #[error("Invalid input: {parameter} = '{value}' is not valid. {suggestion}")]
    InvalidInput {
        parameter: String,
        value: String,
        suggestion: String,
    },

    /// Missing required parameter
    #[error("Missing required parameter: '{parameter}' is required for operation '{operation}'. {suggestion}")]
    MissingParameter {
        parameter: String,
        operation: String,
        suggestion: String,
    },

    /// Invalid UUID format
    #[error("Invalid UUID: '{uuid_string}' is not a valid UUID format. Ensure it follows the standard UUID format (e.g., 550e8400-e29b-41d4-a716-************).")]
    InvalidUuid { uuid_string: String },

    // === System and Resource Errors ===
    /// System resource exhausted
    #[error("Resource exhausted: {resource_type} limit reached. {suggestion}")]
    ResourceExhausted {
        resource_type: String,
        suggestion: String,
    },

    /// Internal system error
    #[error("Internal error: {details}. This is likely a bug - please report it with the error details.")]
    Internal { details: String },

    /// Feature not implemented
    #[error(
        "Feature not implemented: {feature}. This feature may be available in a future version."
    )]
    NotImplemented { feature: String },

    /// Dependency error
    #[error("Dependency error: {dependency} failed - {reason}. Ensure all required dependencies are properly installed and configured.")]
    Dependency { dependency: String, reason: String },
}

impl ClientError {
    /// Create a Session error
    pub fn session_error(client_id: impl Into<String>, details: impl Into<String>) -> Self {
        Self::Session {
            client_id: client_id.into(),
            details: details.into(),
        }
    }

    /// Create a KeyManagement error
    pub fn key_management_error(operation: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::KeyManagement {
            operation: operation.into(),
            reason: reason.into(),
        }
    }

    /// Create a Server error
    pub fn server_error(status_code: u16, message: impl Into<String>) -> Self {
        Self::Server {
            status_code,
            message: message.into(),
        }
    }

    /// Create an InvalidConfiguration error
    pub fn invalid_configuration(
        parameter: impl Into<String>,
        value: impl Into<String>,
        suggestion: impl Into<String>,
    ) -> Self {
        Self::InvalidConfiguration {
            parameter: parameter.into(),
            value: value.into(),
            suggestion: suggestion.into(),
        }
    }

    /// Create a WebSocketWithContext error for better error reporting
    pub fn websocket_with_context(
        context: impl Into<String>,
        source: tokio_tungstenite::tungstenite::Error,
    ) -> Self {
        Self::WebSocketWithContext {
            context: context.into(),
            source,
        }
    }

    /// Create an InvalidEventPayload error
    pub fn invalid_event_payload(details: impl Into<String>) -> Self {
        Self::InvalidEventPayload {
            details: details.into(),
        }
    }

    /// Create a StateSaveFailed error
    pub fn state_save_failed(details: impl Into<String>) -> Self {
        Self::StateSaveFailed {
            details: details.into(),
        }
    }

    /// Create a StateCorrupted error
    pub fn state_corrupted(details: impl Into<String>) -> Self {
        Self::StateCorrupted {
            details: details.into(),
        }
    }

    /// Create a StateIncompatible error
    pub fn state_incompatible(details: impl Into<String>) -> Self {
        Self::StateIncompatible {
            details: details.into(),
        }
    }

    /// Create a NotInitialized error
    pub fn not_initialized(operation: impl Into<String>) -> Self {
        Self::NotInitialized {
            operation: operation.into(),
        }
    }

    /// Create a FileTransfer error
    pub fn file_transfer_error(transfer_id: Uuid, reason: impl Into<String>) -> Self {
        Self::FileTransfer {
            transfer_id,
            reason: reason.into(),
        }
    }

    /// Create an EncryptionFailed error
    pub fn encryption_failed(recipient_id: &PeerId, reason: impl Into<String>) -> Self {
        Self::EncryptionFailed {
            recipient_id: recipient_id.clone(),
            reason: reason.into(),
        }
    }

    /// Create a DecryptionFailed error
    pub fn decryption_failed(sender_id: &PeerId, reason: impl Into<String>) -> Self {
        Self::DecryptionFailed {
            sender_id: sender_id.clone(),
            reason: reason.into(),
        }
    }

    /// Create a Json error
    pub fn json_error(operation: impl Into<String>, reason: impl Into<String>) -> Self {
        Self::Json {
            operation: operation.into(),
            reason: reason.into(),
        }
    }

    /// Create an InvalidState error
    pub fn invalid_state(
        operation: impl Into<String>,
        current_state: impl Into<String>,
        expected_state: impl Into<String>,
    ) -> Self {
        Self::InvalidState {
            operation: operation.into(),
            current_state: current_state.into(),
            expected_state: expected_state.into(),
        }
    }

    /// Create an Internal error
    pub fn internal_error(details: impl Into<String>) -> Self {
        Self::Internal {
            details: details.into(),
        }
    }

    /// Create a CryptoError
    pub fn crypto_error(operation: impl Into<String>, details: impl Into<String>) -> Self {
        Self::CryptoError {
            operation: operation.into(),
            details: details.into(),
        }
    }
}

/// Convenience type alias for Results using ClientError
pub type ClientResult<T> = Result<T, ClientError>;

#[cfg(test)]
mod tests;

/// Convert from standard I/O errors to ClientError
impl From<std::io::Error> for ClientError {
    fn from(err: std::io::Error) -> Self {
        Self::FileIo {
            operation: "I/O operation".to_string(),
            path: "unknown".to_string(),
            reason: err.to_string(),
        }
    }
}

/// Convert from JSON errors to ClientError
impl From<serde_json::Error> for ClientError {
    fn from(err: serde_json::Error) -> Self {
        Self::Json {
            operation: "JSON processing".to_string(),
            reason: err.to_string(),
        }
    }
}

/// Convert from UUID parsing errors to ClientError
impl From<uuid::Error> for ClientError {
    fn from(err: uuid::Error) -> Self {
        Self::InvalidUuid {
            uuid_string: format!("UUID parsing error: {}", err),
        }
    }
}

/// Convert from URL parsing errors to ClientError
impl From<url::ParseError> for ClientError {
    fn from(err: url::ParseError) -> Self {
        Self::InvalidServerUrl {
            url: "unknown".to_string(),
            reason: err.to_string(),
        }
    }
}

/// Convert from anyhow errors to ClientError
impl From<anyhow::Error> for ClientError {
    fn from(err: anyhow::Error) -> Self {
        Self::Internal {
            details: err.to_string(),
        }
    }
}

/// Convert from InvalidPeerId errors to ClientError
impl From<indidus_shared::validation::InvalidPeerId> for ClientError {
    fn from(err: indidus_shared::validation::InvalidPeerId) -> Self {
        Self::InvalidInput {
            parameter: "peer_id".to_string(),
            value: err.to_string(),
            suggestion:
                "Provide a valid peer ID (4-64 characters, alphanumeric, underscore, or hyphen)"
                    .to_string(),
        }
    }
}
