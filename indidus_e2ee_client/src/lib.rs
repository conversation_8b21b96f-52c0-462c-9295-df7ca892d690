//! # Indidus E2EE Client SDK
//!
//! A Rust client SDK for end-to-end encrypted messaging using the Indidus Signal Protocol.
//! This crate provides high-level APIs for establishing secure sessions, sending encrypted
//! messages, and managing cryptographic state.
//!
//! ## Features
//!
//! - **Easy Session Management**: Simple APIs for establishing and managing encrypted sessions
//! - **Message Encryption**: Send and receive end-to-end encrypted messages
//! - **File Transfer**: Secure file sharing with encryption
//! - **Identity Management**: Handle identity keys and authentication
//! - **Server Integration**: Built-in support for Indidus E2EE server communication
//!
//! ## Quick Start
//!
//! ```rust
//! use indidus_e2ee_client::{Client, ClientConfig};
//! use url::Url;
//!
//! # fn main() -> Result<(), Box<dyn std::error::Error>> {
//! // Create a client configuration
//! let config = ClientConfig::new(Url::parse("wss://your-server.com")?)
//!     .with_display_name("My Client".to_string())
//!     .with_debug_mode(true);
//!
//! // Create a new client instance
//! let client = Client::new(config)?;
//!
//! println!("Client ID: {}", client.client_id());
//! # Ok(())
//! # }
//! ```

pub mod client;
pub mod error;
pub mod framing;

// Re-export main types for convenience
pub use client::{
    Client, ClientConfig, ClientEvent, ConnectionState, FileTransferStatus, TransferStatus,
};
pub use error::{ClientError, ClientResult};
pub use framing::EncryptedMessageFrame;