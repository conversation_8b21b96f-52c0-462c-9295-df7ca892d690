//! API client for server communication
//!
//! This module provides a structured way to communicate with the Indidus E2EE server,
//! ensuring that server URLs are always configured at compile-time and providing
//! robust error handling for all network operations.

use crate::error::{C<PERSON>Error, ClientResult};

/// API client for communicating with the Indidus E2EE server
///
/// This struct encapsulates all server communication logic and ensures that
/// a server URL is always configured when the client is instantiated. This
/// design makes it impossible to attempt network requests without a properly
/// configured endpoint.
///
/// # Examples
///
/// ```rust,no_run
/// # use indidus_e2ee_client::client::api::ApiClient;
/// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
/// let api_client = ApiClient::new("https://server.example.com".to_string())?;
/// let bundle = api_client.fetch_prekey_bundle("user-123").await?;
/// # Ok(())
/// # }
/// ```
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ApiClient {
    /// Base URL for the server (e.g., "https://server.example.com")
    base_url: String,
    /// HTTP client for making requests
    http_client: reqwest::Client,
}

impl ApiClient {
    /// Create a new API client with the specified base URL
    ///
    /// This constructor validates the base URL and creates a configured HTTP client.
    /// The URL validation ensures that only valid HTTP/HTTPS URLs are accepted.
    ///
    /// # Arguments
    /// * `base_url` - The base URL for the server (must be a valid HTTP/HTTPS URL)
    ///
    /// # Returns
    /// A new `ApiClient` instance ready for making requests
    ///
    /// # Errors
    /// - `ClientError::InvalidServerUrl` if the base URL is invalid or malformed
    /// - `ClientError::Internal` if the HTTP client cannot be created
    ///
    /// # Examples
    /// ```rust,no_run
    /// # use indidus_e2ee_client::client::api::ApiClient;
    /// # fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let api_client = ApiClient::new("https://server.example.com".to_string())?;
    /// # Ok(())
    /// # }
    /// ```
    pub fn new(base_url: String) -> ClientResult<Self> {
        // Validate the base URL
        let parsed_url = url::Url::parse(&base_url).map_err(|e| {
            ClientError::InvalidServerUrl {
                url: base_url.clone(),
                reason: format!("Invalid URL format: {}", e),
            }
        })?;

        // Ensure the scheme is HTTP, HTTPS, WS, or WSS
        // Note: WS/WSS schemes are allowed for WebSocket connections, but the ApiClient
        // will convert them to HTTP/HTTPS for REST API calls
        match parsed_url.scheme() {
            "http" | "https" | "ws" | "wss" => {},
            scheme => {
                return Err(ClientError::InvalidServerUrl {
                    url: base_url,
                    reason: format!("Unsupported URL scheme '{}'. Only 'http', 'https', 'ws', and 'wss' are supported.", scheme),
                });
            }
        }

        // Ensure the host is present
        if parsed_url.host().is_none() {
            return Err(ClientError::InvalidServerUrl {
                url: base_url,
                reason: "URL must contain a valid host".to_string(),
            });
        }

        // Create the HTTP client with reasonable defaults
        let http_client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| {
                ClientError::internal_error(format!("Failed to create HTTP client: {}", e))
            })?;

        Ok(Self {
            base_url,
            http_client,
        })
    }

    /// Fetch a peer's pre-key bundle from the server
    ///
    /// Performs a real HTTP GET request to fetch a pre-key bundle from the server.
    /// This function handles network errors, non-200 status codes, and JSON 
    /// deserialization errors gracefully.
    ///
    /// # Arguments
    /// * `user_id` - The ID of the user whose pre-key bundle to fetch
    ///
    /// # Returns
    /// The user's pre-key bundle on success
    ///
    /// # Errors
    /// - `ClientError::Network` for network connectivity issues
    /// - `ClientError::Server` for HTTP error status codes (404, 500, etc.)
    /// - `ClientError::Json` for JSON deserialization failures
    ///
    /// # Examples
    /// ```rust,no_run
    /// # use indidus_e2ee_client::client::api::ApiClient;
    /// # async fn example() -> Result<(), Box<dyn std::error::Error>> {
    /// let api_client = ApiClient::new("https://server.example.com".to_string())?;
    /// let bundle = api_client.fetch_prekey_bundle("user-123").await?;
    /// # Ok(())
    /// # }
    /// ```
    pub async fn fetch_prekey_bundle(
        &self,
        user_id: &str,
    ) -> ClientResult<indidus_signal_protocol::crypto::bundle::PreKeyBundle> {
        // Convert WebSocket URLs to HTTP URLs for REST API calls
        let http_base_url = self.base_url
            .replace("ws://", "http://")
            .replace("wss://", "https://");
        
        // Construct the request URL
        let request_url = format!("{}/prekey/{}", http_base_url, user_id);

        // Perform the HTTP GET request
        let response = self.http_client
            .get(&request_url)
            .send()
            .await
            .map_err(ClientError::Network)?;

        // Check if the response status is successful
        if response.status().is_success() {
            // Deserialize the JSON response to PreKeyBundle
            let bundle = response
                .json::<indidus_signal_protocol::crypto::bundle::PreKeyBundle>()
                .await
                .map_err(|e| {
                    ClientError::json_error(
                        "deserialize PreKeyBundle from server response",
                        e.to_string()
                    )
                })?;
            
            Ok(bundle)
        } else {
            // Return a server error with the status code and message
            let status_code = response.status().as_u16();
            let error_message = response
                .text()
                .await
                .unwrap_or_else(|_| format!("HTTP {}", status_code));
            
            Err(ClientError::server_error(status_code, error_message))
        }
    }

    /// Get the base URL for this API client
    pub fn base_url(&self) -> &str {
        &self.base_url
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use wiremock::{MockServer, Mock, ResponseTemplate};
    use wiremock::matchers::{method, path};
    use indidus_signal_protocol::crypto::bundle::PreKeyBundle;

    /// Test fixture for setting up a mock server and API client
    /// 
    /// This function creates a wiremock MockServer that can be used to simulate
    /// server responses for testing the ApiClient without requiring a real server.
    /// 
    /// # Returns
    /// A tuple containing (MockServer, ApiClient) ready for testing
    async fn setup_mock_server() -> (MockServer, ApiClient) {
        let mock_server = MockServer::start().await;
        let api_client = ApiClient::new(mock_server.uri())
            .expect("Failed to create ApiClient with mock server URL");
        
        (mock_server, api_client)
    }

    /// Helper function to create a test PreKeyBundle for mock responses
    /// 
    /// This creates a valid PreKeyBundle that can be serialized to JSON
    /// and used in mock server responses for testing.
    fn create_test_prekey_bundle() -> PreKeyBundle {
        // Use the generate_prekey_bundle function from the protocol crate
        // This ensures we create a properly signed bundle
        let identity_key = indidus_signal_protocol::crypto::util::generate_identity_key()
            .expect("Failed to generate identity key");
        
        let (bundle, _signed_prekey, _onetime_prekeys) = 
            indidus_signal_protocol::crypto::bundle::generate_prekey_bundle(&identity_key, 1)
                .expect("Failed to generate prekey bundle");
        
        bundle
    }

    #[tokio::test]
    async fn test_api_client_creation_with_valid_url() {
        let api_client = ApiClient::new("https://example.com".to_string());
        assert!(api_client.is_ok());
        
        let client = api_client.unwrap();
        assert_eq!(client.base_url(), "https://example.com");
    }

    #[tokio::test]
    async fn test_api_client_creation_with_invalid_url() {
        let result = ApiClient::new("not-a-url".to_string());
        assert!(result.is_err());
        
        if let Err(ClientError::InvalidServerUrl { url, reason }) = result {
            assert_eq!(url, "not-a-url");
            assert!(reason.contains("Invalid URL format"));
        } else {
            panic!("Expected InvalidServerUrl error");
        }
    }

    #[tokio::test]
    async fn test_api_client_creation_with_unsupported_scheme() {
        let result = ApiClient::new("ftp://example.com".to_string());
        assert!(result.is_err());
        
        if let Err(ClientError::InvalidServerUrl { url, reason }) = result {
            assert_eq!(url, "ftp://example.com");
            assert!(reason.contains("Unsupported URL scheme"));
        } else {
            panic!("Expected InvalidServerUrl error");
        }
    }

    #[tokio::test]
    async fn test_api_client_creation_with_websocket_url() {
        let api_client = ApiClient::new("ws://example.com".to_string());
        assert!(api_client.is_ok());
        
        let client = api_client.unwrap();
        assert_eq!(client.base_url(), "ws://example.com");
    }

    #[tokio::test]
    async fn test_api_client_creation_with_secure_websocket_url() {
        let api_client = ApiClient::new("wss://example.com".to_string());
        assert!(api_client.is_ok());
        
        let client = api_client.unwrap();
        assert_eq!(client.base_url(), "wss://example.com");
    }

    #[tokio::test]
    async fn test_api_client_creation_with_missing_host() {
        let result = ApiClient::new("https://".to_string());
        assert!(result.is_err());
        
        if let Err(ClientError::InvalidServerUrl { url, reason }) = result {
            assert_eq!(url, "https://");
            assert!(reason.contains("host"), "Expected host-related error message, got: {}", reason);
        } else {
            panic!("Expected InvalidServerUrl error, got: {:?}", result);
        }
    }

    #[tokio::test]
    async fn test_mock_server_setup() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Verify the mock server is running and the API client is configured correctly
        assert!(mock_server.uri().starts_with("http://127.0.0.1:"));
        assert_eq!(api_client.base_url(), mock_server.uri());
    }

    #[tokio::test]
    async fn test_api_client_http_client_creation_success() {
        // Test the successful HTTP client creation path (lines 88-93)
        let result = ApiClient::new("https://valid.example.com".to_string());
        
        // This should succeed and cover the HTTP client creation code path
        assert!(result.is_ok());
        let client = result.unwrap();
        assert_eq!(client.base_url(), "https://valid.example.com");
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_success_200_ok() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Create a test PreKeyBundle to return from the mock server
        let test_bundle = create_test_prekey_bundle();
        let bundle_json = test_bundle.to_json().expect("Failed to serialize test bundle");
        
        // Configure the mock server to respond with 200 OK and the test bundle
        Mock::given(method("GET"))
            .and(path("/prekey/test-user-123"))
            .respond_with(
                ResponseTemplate::new(200)
                    .set_body_string(bundle_json.clone())
                    .insert_header("content-type", "application/json")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("test-user-123").await;
        
        // Assert that the request was successful
        assert!(result.is_ok(), "Expected successful result, got: {:?}", result);
        
        let returned_bundle = result.unwrap();
        
        // Verify that the returned bundle matches the expected data
        // We'll compare the JSON representations since PreKeyBundle doesn't implement PartialEq
        let returned_json = returned_bundle.to_json().expect("Failed to serialize returned bundle");
        assert_eq!(returned_json, bundle_json, "Returned bundle JSON doesn't match expected");
        
        // Verify that the mock server received exactly one request
        // This ensures our API client made the correct HTTP call
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_404_not_found() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Configure the mock server to respond with 404 Not Found
        Mock::given(method("GET"))
            .and(path("/prekey/nonexistent-user"))
            .respond_with(
                ResponseTemplate::new(404)
                    .set_body_string("User not found")
                    .insert_header("content-type", "text/plain")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("nonexistent-user").await;
        
        // Assert that the request failed with the expected error
        assert!(result.is_err(), "Expected error result for 404 response");
        
        match result.unwrap_err() {
            ClientError::Server { status_code, message } => {
                assert_eq!(status_code, 404, "Expected 404 status code");
                assert_eq!(message, "User not found", "Expected 'User not found' message");
            }
            other => panic!("Expected Server error, got: {:?}", other),
        }
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_500_internal_server_error() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Configure the mock server to respond with 500 Internal Server Error
        Mock::given(method("GET"))
            .and(path("/prekey/test-user"))
            .respond_with(
                ResponseTemplate::new(500)
                    .set_body_string("Internal server error occurred")
                    .insert_header("content-type", "text/plain")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("test-user").await;
        
        // Assert that the request failed with the expected error
        assert!(result.is_err(), "Expected error result for 500 response");
        
        match result.unwrap_err() {
            ClientError::Server { status_code, message } => {
                assert_eq!(status_code, 500, "Expected 500 status code");
                assert_eq!(message, "Internal server error occurred", "Expected server error message");
            }
            other => panic!("Expected Server error, got: {:?}", other),
        }
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_403_forbidden() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Configure the mock server to respond with 403 Forbidden
        Mock::given(method("GET"))
            .and(path("/prekey/restricted-user"))
            .respond_with(
                ResponseTemplate::new(403)
                    .set_body_string("Access denied")
                    .insert_header("content-type", "text/plain")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("restricted-user").await;
        
        // Assert that the request failed with the expected error
        assert!(result.is_err(), "Expected error result for 403 response");
        
        match result.unwrap_err() {
            ClientError::Server { status_code, message } => {
                assert_eq!(status_code, 403, "Expected 403 status code");
                assert_eq!(message, "Access denied", "Expected access denied message");
            }
            other => panic!("Expected Server error, got: {:?}", other),
        }
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_json_deserialization_failure() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Configure the mock server to respond with 200 OK but invalid JSON
        Mock::given(method("GET"))
            .and(path("/prekey/test-user"))
            .respond_with(
                ResponseTemplate::new(200)
                    .set_body_string(r#"{"invalid": "json", "missing_fields":}"#) // Malformed JSON
                    .insert_header("content-type", "application/json")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("test-user").await;
        
        // Assert that the request failed with a JSON deserialization error
        assert!(result.is_err(), "Expected error result for malformed JSON response");
        
        match result.unwrap_err() {
            ClientError::Json { operation, reason } => {
                assert_eq!(operation, "deserialize PreKeyBundle from server response", "Expected JSON deserialization operation");
                assert!(reason.contains("JSON") || reason.contains("json") || reason.contains("decoding") || reason.contains("expected"), "Expected JSON-related error message, got: {}", reason);
            }
            other => panic!("Expected Json error, got: {:?}", other),
        }
    }

    #[tokio::test]
    async fn test_fetch_prekey_bundle_invalid_json_structure() {
        let (mock_server, api_client) = setup_mock_server().await;
        
        // Configure the mock server to respond with 200 OK but JSON that doesn't match PreKeyBundle structure
        Mock::given(method("GET"))
            .and(path("/prekey/test-user"))
            .respond_with(
                ResponseTemplate::new(200)
                    .set_body_string(r#"{"completely": "different", "structure": true, "number": 42}"#)
                    .insert_header("content-type", "application/json")
            )
            .mount(&mock_server)
            .await;
        
        // Call the fetch_prekey_bundle method
        let result = api_client.fetch_prekey_bundle("test-user").await;
        
        // Assert that the request failed with a JSON deserialization error
        assert!(result.is_err(), "Expected error result for invalid JSON structure");
        
        match result.unwrap_err() {
            ClientError::Json { operation, reason } => {
                assert_eq!(operation, "deserialize PreKeyBundle from server response", "Expected JSON deserialization operation");
                // The error should indicate missing fields or type mismatches
                assert!(
                    reason.contains("missing") || reason.contains("field") || reason.contains("type"),
                    "Expected field-related error message, got: {}", reason
                );
            }
            other => panic!("Expected Json error, got: {:?}", other),
        }
    }
}