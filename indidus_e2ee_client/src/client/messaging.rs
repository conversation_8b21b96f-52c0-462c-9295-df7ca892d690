use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use indidus_shared::validation::PeerId;

use super::file_transfer::FileTransferStatus;
use crate::client::Client;
use crate::error::{C<PERSON><PERSON>rror, ClientResult};
use indidus_signal_protocol::Session;
use indidus_signal_protocol::{KeyPair, MessageHeader};

/// Internal server message types for WebSocket communication
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub(crate) enum ServerMessage {
    /// Message relay from another client
    MessageRelay {
        /// ID of the sender client
        sender_id: Uuid,
        /// ID of the recipient client (should be this client)
        recipient_id: Uuid,
        /// Encrypted message payload
        encrypted_payload: Vec<u8>,
        /// Message metadata
        metadata: HashMap<String, String>,
        /// Message type identifier
        message_type: String,
        /// Server-assigned message ID
        message_id: Option<Uuid>,
    },

    /// File transfer initiation
    FileTransferInitiated {
        /// ID of the sender client
        sender_id: Uuid,
        /// ID of the recipient client (should be this client)
        recipient_id: Uuid,
        /// Unique transfer ID
        transfer_id: Uuid,
        /// File metadata
        file_name: String,
        file_size: u64,
        file_hash: String,
        /// Upload URL for the sender
        upload_url: String,
        /// Download URL for the recipient
        download_url: String,
        /// Transfer expiration time
        expires_at: std::time::SystemTime,
    },

    /// File transfer status update
    FileTransferStatus {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Updated status information
        status: FileTransferStatus,
    },

    /// Pre-key bundle request
    PreKeyRequest {
        /// ID of the requesting client
        requester_id: Uuid,
        /// ID of the target client (should be this client)
        target_client_id: Uuid,
        /// Number of pre-keys requested
        count: u32,
    },

    /// Message delivery confirmation
    MessageDelivered {
        /// Server-assigned message ID
        message_id: Uuid,
        /// ID of the recipient client
        recipient_id: Uuid,
    },

    /// Client presence update
    ClientPresence {
        /// ID of the client
        client_id: Uuid,
        /// Whether the client is online
        online: bool,
        /// Optional display name
        display_name: Option<String>,
    },

    /// Server error notification
    Error {
        /// Error code
        error_code: String,
        /// Error message
        error_message: String,
        /// Optional request ID that caused the error
        request_id: Option<String>,
    },

    /// Connection acknowledgment
    ConnectionAck {
        /// Assigned session ID
        session_id: String,
        /// Server timestamp
        server_time: std::time::SystemTime,
    },

    /// Ping message for keepalive
    Ping {
        /// Timestamp when ping was sent
        timestamp: std::time::SystemTime,
    },

    /// Pong response to ping
    Pong {
        /// Original ping timestamp
        ping_timestamp: std::time::SystemTime,
        /// Server timestamp when pong was sent
        pong_timestamp: std::time::SystemTime,
    },

    /// File chunk data response
    ChunkData {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Index of this chunk (0-based)
        chunk_index: u64,
        /// Encrypted chunk data
        encrypted_data: Vec<u8>,
        /// Whether this is the final chunk
        is_final: bool,
        /// Size of the original (unencrypted) chunk
        original_size: usize,
    },

    /// File transfer offer from another client
    FileTransferOffer {
        /// ID of the sender client
        sender_id: Uuid,
        /// Unique transfer ID
        transfer_id: Uuid,
        /// File metadata
        file_name: String,
        file_size: u64,
        file_hash: String,
        /// Number of chunks
        chunk_count: u64,
        /// Encryption key for decryption
        encryption_key: Vec<u8>,
        /// Expiration time for the offer
        expires_at: std::time::SystemTime,
    },
}

/// Client-to-server message types for WebSocket communication
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub(crate) enum ClientMessage {
    /// Send a message to another client
    SendMessage {
        /// ID of the recipient client
        recipient_id: PeerId,
        /// Encrypted message payload
        encrypted_payload: Vec<u8>,
        /// Message metadata
        metadata: HashMap<String, String>,
        /// Message type identifier
        message_type: String,
        /// Optional client-side message ID for tracking
        client_message_id: Option<String>,
    },

    /// Initiate a file transfer
    InitiateFileTransfer {
        /// ID of the recipient client
        recipient_id: Uuid,
        /// File metadata
        file_name: String,
        file_size: u64,
        file_hash: String,
        /// Encryption parameters
        encryption_key: Vec<u8>,
        chunk_size: u32,
    },

    /// Respond to a pre-key request
    PreKeyResponse {
        /// ID of the requesting client
        requester_id: Uuid,
        /// List of available pre-keys
        prekeys: Vec<Vec<u8>>,
        /// Whether more pre-keys are available
        has_more: bool,
    },

    /// Acknowledge message delivery
    MessageAck {
        /// Server-assigned message ID
        message_id: Uuid,
    },

    /// Update client presence
    UpdatePresence {
        /// Whether the client is online
        online: bool,
        /// Optional display name update
        display_name: Option<String>,
    },

    /// Pong response to server ping
    Pong {
        /// Original ping timestamp from server
        ping_timestamp: std::time::SystemTime,
    },

    /// Authentication message
    Authenticate {
        /// Client ID
        client_id: Uuid,
        /// Authentication credentials
        credentials: Vec<u8>,
        /// Optional display name
        display_name: Option<String>,
    },

    /// Request a specific chunk for download
    RequestChunk {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Index of the chunk to request (0-based)
        chunk_index: u64,
    },

    /// Accept a file transfer offer
    AcceptFileTransfer {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Local file path where the file should be saved
        save_path: String,
    },

    /// Reject a file transfer offer
    RejectFileTransfer {
        /// Unique transfer ID
        transfer_id: Uuid,
        /// Optional reason for rejection
        reason: Option<String>,
    },
}

impl Client {
    /// Send a pre-encrypted message to another client through the WebSocket connection
    ///
    /// This is the low-level method for sending already-encrypted messages. Most users
    /// should use the high-level `send_message` method instead.
    ///
    /// # Arguments
    /// * `recipient_id` - The UUID of the recipient client
    /// * `encrypted_payload` - The encrypted message payload
    /// * `metadata` - Optional message metadata
    /// * `message_type` - The type of message being sent
    ///
    /// # Returns
    /// Success or error if the message cannot be sent
    ///
    /// # Errors
    /// - `ClientError::General` if not connected or the message channel is closed
    pub async fn send_encrypted_message(
        &self,
        recipient_id: PeerId,
        encrypted_payload: Vec<u8>,
        metadata: HashMap<String, String>,
        message_type: String,
    ) -> ClientResult<()> {
        let message = ClientMessage::SendMessage {
            recipient_id,
            encrypted_payload,
            metadata,
            message_type,
            client_message_id: None,
        };

        match &self.message_sender {
            Some(sender) => {
                sender.send(message).await.map_err(|_| {
                    ClientError::internal_error("Failed to send message: channel closed")
                })?;
                Ok(())
            }
            None => Err(ClientError::invalid_state(
                "send_encrypted_message",
                "disconnected",
                "connected",
            )),
        }
    }

    pub async fn send_message(&mut self, peer_id: PeerId, content: &[u8]) -> ClientResult<()> {
        // Check if client is initialized
        if !self.is_initialized() {
            return Err(ClientError::not_initialized("send_message"));
        }

        // Check if client is connected
        if !self.is_connected() {
            return Err(ClientError::invalid_state(
                "send_message",
                "disconnected",
                "connected to server",
            ));
        }

        // Validate input
        if content.is_empty() {
            return Err(ClientError::InvalidInput {
                parameter: "content".to_string(),
                value: "empty".to_string(),
                suggestion: "Message content cannot be empty".to_string(),
            });
        }

        // Check if we have an existing session with this peer
        if self.sessions.contains_key(&peer_id) {
            // Existing session - use normal Double Ratchet encryption
            self.send_message_existing_session(peer_id, content).await
        } else {
            // No existing session - initiate X3DH handshake
            self.send_message_with_x3dh_handshake(peer_id, content)
                .await
        }
    }

    /// Send a message using an existing session
    async fn send_message_existing_session(
        &mut self,
        peer_id: PeerId,
        content: &[u8],
    ) -> ClientResult<()> {
        use crate::framing::EncryptedMessageFrame;

        // Get the session for this peer
        let session = self.sessions.get_mut(&peer_id).ok_or_else(|| {
            ClientError::invalid_state(
                "send_message_existing_session",
                "no session",
                "existing session",
            )
        })?;

        // Encrypt the message content using the unified Session
        let (header, ciphertext) = session
            .encrypt(content)
            .map_err(|e| ClientError::encryption_failed(&peer_id, e.to_string()))?;

        // Serialize the header to be included in the frame
        let header_bytes = serde_json::to_vec(&header)
            .map_err(|e| ClientError::json_error("serialize message header", e.to_string()))?;

        // Create an EncryptedMessageFrame with the message components
        // For Signal protocol: recipient_key = peer_id bytes, ephemeral_key = DH public key,
        // ciphertext = header + encrypted content, nonce = empty (not used in Double Ratchet)
        let frame = EncryptedMessageFrame::new(
            peer_id.as_str().as_bytes().to_vec(),  // recipient_key: peer identifier
            header.dh_public_key.as_bytes().to_vec(), // ephemeral_key: current DH public key
            {
                // ciphertext: combine header and encrypted content
                let mut combined_ciphertext = Vec::new();
                combined_ciphertext.extend_from_slice(&(header_bytes.len() as u32).to_le_bytes());
                combined_ciphertext.extend_from_slice(&header_bytes);
                combined_ciphertext.extend_from_slice(&ciphertext);
                combined_ciphertext
            },
            Vec::new(),                            // nonce: not used in Double Ratchet
        );

        // Serialize the frame to get the encrypted payload
        let encrypted_payload = frame
            .serialize()
            .map_err(|e| ClientError::internal_error(format!("Failed to serialize EncryptedMessageFrame: {}", e)))?;

        // Create metadata for the message
        let mut metadata = HashMap::new();
        metadata.insert(
            "timestamp".to_string(),
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
                .to_string(),
        );
        metadata.insert(
            "content_type".to_string(),
            "application/octet-stream".to_string(),
        );

        // Send the encrypted message via WebSocket
        self.send_encrypted_message(
            peer_id,
            encrypted_payload,
            metadata,
            "encrypted".to_string(),
        )
        .await
        .map_err(|e| ClientError::internal_error(e.to_string()))
    }

    /// Send a message by initiating an X3DH handshake for a new session
    async fn send_message_with_x3dh_handshake(
        &mut self,
        peer_id: PeerId,
        content: &[u8],
    ) -> ClientResult<()> {
        use crate::framing::EncryptedMessageFrame;
        use indidus_signal_protocol::crypto::x3dh::{
            create_initial_message, x3dh_initiator, X3dhParams,
        };

        // Step 1: Fetch the peer's pre-key bundle from the server
        let peer_bundle = self.fetch_prekey_bundle(&peer_id).await?;

        // Step 2: Convert the peer's identity key to a verifying key for signature verification
        use indidus_signal_protocol::crypto::util::x25519_to_ed25519_verifying_key;
        let peer_verifying_key = x25519_to_ed25519_verifying_key(&peer_bundle.identity_key)
            .map_err(|e| ClientError::crypto_error("convert peer identity key", e.to_string()))?;

        // Step 3: Perform X3DH key agreement as the initiator (with internal signature verification)
        let x3dh_params = X3dhParams {
            identity_key: <std::option::Option<KeyPair> as Clone>::clone(&self.identity_key)
                .ok_or_else(|| {
                    ClientError::invalid_state(
                        "send_message",
                        "no identity key",
                        "initialized client",
                    )
                })?,
            recipient_bundle: peer_bundle.clone(),
            onetime_prekey_index: Some(0), // Use the first available one-time pre-key
        };

        let x3dh_result = x3dh_initiator(x3dh_params, &peer_verifying_key)
            .map_err(|e| ClientError::crypto_error("X3DH handshake", e.to_string()))?;

        // Step 4: Create initial message for the recipient
        let initial_message = create_initial_message(
            <std::option::Option<KeyPair> as Clone>::clone(&self.identity_key)
                .unwrap()
                .public_key(),
            &x3dh_result,
        );

        // Step 5: Initialize a new Double Ratchet session as the initiator
        let mut session = Session::new_initiator(
            x3dh_result.shared_secret,
            &peer_bundle,                         // Use the entire bundle
            1000,                                 // max_skip
            Some(format!("session-{}", peer_id)), // session_id
        )
        .map_err(|e| ClientError::crypto_error("session initialization", e.to_string()))?;

        // Step 6: Encrypt the message using the new session
        let (header, ciphertext) = session
            .encrypt(content)
            .map_err(|e| ClientError::encryption_failed(&peer_id, e.to_string()))?;

        // Step 7: Store the session for future messages
        self.sessions.insert(peer_id.clone(), session);

        // Step 8: Create the pre-key message payload using EncryptedMessageFrame
        let initial_message_bytes = serde_json::to_vec(&initial_message)
            .map_err(|e| ClientError::json_error("serialize initial message", e.to_string()))?;

        let header_bytes = serde_json::to_vec(&header)
            .map_err(|e| ClientError::json_error("serialize message header", e.to_string()))?;

        // Create an EncryptedMessageFrame for the pre-key message
        // For pre-key messages: recipient_key = initial_message, ephemeral_key = DH public key,
        // ciphertext = header + encrypted content, nonce = empty
        let frame = EncryptedMessageFrame::new(
            initial_message_bytes,                     // recipient_key: X3DH initial message
            header.dh_public_key.as_bytes().to_vec(), // ephemeral_key: current DH public key
            {
                // ciphertext: combine header and encrypted content
                let mut combined_ciphertext = Vec::new();
                combined_ciphertext.extend_from_slice(&(header_bytes.len() as u32).to_le_bytes());
                combined_ciphertext.extend_from_slice(&header_bytes);
                combined_ciphertext.extend_from_slice(&ciphertext);
                combined_ciphertext
            },
            Vec::new(),                                // nonce: not used in Double Ratchet
        );

        // Serialize the frame to get the pre-key payload
        let prekey_payload = frame
            .serialize()
            .map_err(|e| ClientError::internal_error(format!("Failed to serialize pre-key EncryptedMessageFrame: {}", e)))?;

        // Step 9: Create metadata for the pre-key message
        let mut metadata = HashMap::new();
        metadata.insert(
            "timestamp".to_string(),
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
                .to_string(),
        );
        metadata.insert(
            "content_type".to_string(),
            "application/octet-stream".to_string(),
        );
        metadata.insert("message_type".to_string(), "prekey".to_string());

        // Step 10: Send the pre-key message via WebSocket
        self.send_encrypted_message(peer_id, prekey_payload, metadata, "prekey".to_string())
            .await
            .map_err(|e| ClientError::internal_error(e.to_string()))
    }

    /// Fetch a peer's pre-key bundle from the server
    ///
    /// Uses the configured ApiClient to fetch a pre-key bundle from the server.
    /// This ensures that the server URL is always properly configured and provides
    /// robust error handling for all network operations.
    ///
    /// # Arguments
    /// * `peer_id` - The UUID of the peer whose pre-key bundle to fetch
    ///
    /// # Returns
    /// The peer's pre-key bundle on success
    ///
    /// # Errors
    /// - `ClientError::Network` for network connectivity issues
    /// - `ClientError::Server` for HTTP error status codes (404, 500, etc.)
    /// - `ClientError::Json` for JSON deserialization failures
    async fn fetch_prekey_bundle(
        &self,
        peer_id: &PeerId,
    ) -> ClientResult<indidus_signal_protocol::crypto::bundle::PreKeyBundle> {
        // Use the ApiClient to fetch the pre-key bundle
        // This ensures the server URL is always configured and provides structured error handling
        self.api_client
            .fetch_prekey_bundle(peer_id.as_str())
            .await
    }

    pub async fn decrypt_message(
        &mut self,
        sender_id: PeerId,
        encrypted_payload: &[u8],
    ) -> ClientResult<Vec<u8>> {
        // Check if client is initialized
        if !self.is_initialized() {
            return Err(ClientError::not_initialized("decrypt_message"));
        }

        // Validate input
        if encrypted_payload.is_empty() {
            return Err(ClientError::InvalidMessageFormat {
                details: "Encrypted payload cannot be empty".to_string(),
            });
        }

        // Check if we have an existing session with this sender
        if self.sessions.contains_key(&sender_id) {
            // Existing session - use normal Double Ratchet decryption
            self.decrypt_message_existing_session(sender_id, encrypted_payload)
                .await
        } else {
            // No existing session - process as X3DH pre-key message
            self.decrypt_prekey_message(sender_id, encrypted_payload)
                .await
        }
    }

    /// Decrypt a message using an existing session
    async fn decrypt_message_existing_session(
        &mut self,
        sender_id: PeerId,
        encrypted_payload: &[u8],
    ) -> ClientResult<Vec<u8>> {
        use crate::framing::EncryptedMessageFrame;

        // Deserialize the encrypted payload using EncryptedMessageFrame
        let frame = EncryptedMessageFrame::deserialize(encrypted_payload)
            .map_err(|e| ClientError::InvalidMessageFormat {
                details: format!("Failed to deserialize EncryptedMessageFrame: {}", e),
            })?;

        // Validate that this message is for us (recipient_key should match our peer_id)
        let expected_recipient = sender_id.as_str().as_bytes();
        if frame.recipient_key != expected_recipient {
            return Err(ClientError::InvalidMessageFormat {
                details: "Message recipient key does not match expected sender".to_string(),
            });
        }

        // Extract the combined ciphertext (header + encrypted content)
        let combined_ciphertext = &frame.ciphertext;

        // Parse the combined ciphertext: [header_length (4 bytes)] + [header] + [ciphertext]
        if combined_ciphertext.len() < 4 {
            return Err(ClientError::InvalidMessageFormat {
                details: "Combined ciphertext too short to contain header length".to_string(),
            });
        }

        // Extract header length
        let header_length = u32::from_le_bytes([
            combined_ciphertext[0],
            combined_ciphertext[1],
            combined_ciphertext[2],
            combined_ciphertext[3],
        ]) as usize;

        // Validate header length
        if header_length == 0 || header_length > combined_ciphertext.len() - 4 {
            return Err(ClientError::InvalidMessageFormat {
                details: format!("Invalid header length: {}", header_length),
            });
        }

        // Extract header and ciphertext
        let header_end = 4 + header_length;
        if header_end > combined_ciphertext.len() {
            return Err(ClientError::InvalidMessageFormat {
                details: "Combined ciphertext too short to contain complete header".to_string(),
            });
        }

        let header_bytes = &combined_ciphertext[4..header_end];
        let ciphertext = &combined_ciphertext[header_end..];

        if ciphertext.is_empty() {
            return Err(ClientError::InvalidMessageFormat {
                details: "No ciphertext found in combined payload".to_string(),
            });
        }

        // Deserialize the message header
        let header: MessageHeader = serde_json::from_slice(header_bytes)
            .map_err(|e| ClientError::json_error("deserialize message header", e.to_string()))?;

        // Get the session for this sender
        let session = self.sessions.get_mut(&sender_id).ok_or_else(|| {
            ClientError::invalid_state(
                "decrypt_message_existing_session",
                "no session",
                "existing session",
            )
        })?;

        // Decrypt the message using the unified Session
        let plaintext = session
            .decrypt(&header, ciphertext)
            .map_err(|e| ClientError::decryption_failed(&sender_id, e.to_string()))?;

        Ok(plaintext)
    }

    /// Decrypt a pre-key message and establish a new session
    async fn decrypt_prekey_message(
        &mut self,
        sender_id: PeerId,
        encrypted_payload: &[u8],
    ) -> ClientResult<Vec<u8>> {
        use crate::framing::EncryptedMessageFrame;
        use indidus_signal_protocol::crypto::x3dh::{
            x3dh_recipient, X3dhInitialMessage, X3dhRecipientParams,
        };

        // Deserialize the encrypted payload using EncryptedMessageFrame
        let frame = EncryptedMessageFrame::deserialize(encrypted_payload)
            .map_err(|e| ClientError::InvalidMessageFormat {
                details: format!("Failed to deserialize pre-key EncryptedMessageFrame: {}", e),
            })?;

        // For pre-key messages, the recipient_key contains the X3DH initial message
        let initial_message: X3dhInitialMessage = serde_json::from_slice(&frame.recipient_key)
            .map_err(|e| ClientError::json_error("deserialize initial message from recipient_key", e.to_string()))?;

        // Extract the remote identity key before moving initial_message
        let remote_identity_key = initial_message.identity_key;

        // Extract the combined ciphertext (header + encrypted content)
        let combined_ciphertext = &frame.ciphertext;

        // Parse the combined ciphertext: [header_length (4 bytes)] + [header] + [ciphertext]
        if combined_ciphertext.len() < 4 {
            return Err(ClientError::InvalidMessageFormat {
                details: "Pre-key combined ciphertext too short to contain header length".to_string(),
            });
        }

        // Extract header length
        let header_length = u32::from_le_bytes([
            combined_ciphertext[0],
            combined_ciphertext[1],
            combined_ciphertext[2],
            combined_ciphertext[3],
        ]) as usize;

        if header_length == 0 || header_length > combined_ciphertext.len() - 4 {
            return Err(ClientError::InvalidMessageFormat {
                details: format!(
                    "Invalid header length in pre-key message: {}",
                    header_length
                ),
            });
        }

        // Extract header and ciphertext
        let header_end = 4 + header_length;
        if header_end > combined_ciphertext.len() {
            return Err(ClientError::InvalidMessageFormat {
                details: "Pre-key combined ciphertext too short to contain complete header".to_string(),
            });
        }

        let header_bytes = &combined_ciphertext[4..header_end];
        let ciphertext = &combined_ciphertext[header_end..];

        if ciphertext.is_empty() {
            return Err(ClientError::InvalidMessageFormat {
                details: "No ciphertext found in pre-key message".to_string(),
            });
        }

        // Deserialize the message header
        let header: MessageHeader = serde_json::from_slice(header_bytes).map_err(|e| {
            ClientError::json_error(
                "deserialize message header in pre-key message",
                e.to_string(),
            )
        })?;

        // Get our identity key and signed pre-key
        let identity_key = self.identity_key.clone().ok_or_else(|| {
            ClientError::invalid_state(
                "decrypt_prekey_message",
                "no identity key",
                "initialized client",
            )
        })?;

        let signed_prekey = self.signed_prekey.clone().ok_or_else(|| {
            ClientError::invalid_state(
                "decrypt_prekey_message",
                "no signed prekey",
                "initialized client",
            )
        })?;

        // Find the matching one-time pre-key if one was used
        let onetime_prekey = if let Some(used_opk_public) = initial_message.used_onetime_prekey {
            // Find the matching one-time pre-key in our store
            self.onetime_prekeys
                .iter()
                .find(|opk| opk.public_key() == used_opk_public)
                .cloned()
                .ok_or_else(|| {
                    ClientError::crypto_error(
                        "find one-time pre-key",
                        "Used one-time pre-key not found in local store",
                    )
                })?
        } else {
            // No one-time pre-key was used
            return Err(ClientError::crypto_error(
                "process pre-key message",
                "Pre-key message must use a one-time pre-key",
            ));
        };

        // Perform X3DH as the recipient
        let x3dh_params = X3dhRecipientParams {
            identity_key: identity_key.clone(),
            signed_prekey: signed_prekey.clone(),
            onetime_prekey: Some(onetime_prekey),
            initial_message,
        };

        let x3dh_result = x3dh_recipient(x3dh_params.clone())
            .map_err(|e| ClientError::crypto_error("X3DH recipient handshake", e.to_string()))?;

        // Initialize a new Double Ratchet session as the responder
        let mut session = Session::new_responder(
            x3dh_result.shared_secret,
            signed_prekey,
            remote_identity_key, // remote identity key extracted earlier
            1000,                // max_skip
            Some(format!("session-{}", sender_id)), // session_id
        )
        .map_err(|e| {
            ClientError::crypto_error("session initialization as responder", e.to_string())
        })?;

        // Decrypt the message using the new session
        let plaintext = session
            .decrypt(&header, ciphertext)
            .map_err(|e| ClientError::decryption_failed(&sender_id, e.to_string()))?;

        // Store the session for future messages
        self.sessions.insert(sender_id, session);

        // Remove the used one-time pre-key from our store
        if let Some(used_opk_public) = x3dh_params.initial_message.used_onetime_prekey {
            self.onetime_prekeys
                .retain(|opk| opk.public_key() != used_opk_public);
        }

        Ok(plaintext)
    }
}
