#[cfg(test)]
mod tests {
    use crate::client::{Client, ClientConfig};
    use crate::client::events::ClientEvent;
    use crate::client::messaging::ServerMessage;
    use crate::client::connection::ConnectionState;
    use futures_util::{SinkExt, StreamExt};
    use serde_json;
    use std::collections::HashMap;
    use std::net::SocketAddr;
    use std::time::Duration;
    use tokio::net::{TcpListener, TcpStream};
    use tokio::sync::mpsc;
    use tokio::time::timeout;
    use tokio_tungstenite::{
        accept_async, tungstenite::Message, WebSocketStream,
    };
    use url::Url;
    use uuid::Uuid;

    /// Mock WebSocket server for testing client connections
    struct MockWebSocketServer {
        listener: TcpListener,
        addr: SocketAddr,
        shutdown_tx: Option<mpsc::Sender<()>>,
    }

    impl MockWebSocketServer {
        /// Create a new mock WebSocket server
        async fn new() -> Result<Self, Box<dyn std::error::Error>> {
            let listener = TcpListener::bind("127.0.0.1:0").await?;
            let addr = listener.local_addr()?;
            
            Ok(Self {
                listener,
                addr,
                shutdown_tx: None,
            })
        }

        /// Get the WebSocket URL for this mock server
        fn ws_url(&self) -> String {
            format!("ws://127.0.0.1:{}", self.addr.port())
        }

        /// Start the mock server with custom message handling
        async fn start_with_handler<F, Fut>(
            &mut self,
            handler: F,
        ) -> Result<(), Box<dyn std::error::Error>>
        where
            F: Fn(WebSocketStream<TcpStream>) -> Fut + Send + 'static,
            Fut: std::future::Future<Output = ()> + Send + 'static,
        {
            let (shutdown_tx, mut shutdown_rx) = mpsc::channel::<()>(1);
            self.shutdown_tx = Some(shutdown_tx);

            let listener = std::mem::replace(&mut self.listener, TcpListener::bind("127.0.0.1:0").await?);
            
            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        result = listener.accept() => {
                            match result {
                                Ok((stream, _)) => {
                                    let ws_stream = match accept_async(stream).await {
                                        Ok(ws) => ws,
                                        Err(_) => continue,
                                    };
                                    
                                    tokio::spawn(handler(ws_stream));
                                }
                                Err(_) => break,
                            }
                        }
                        _ = shutdown_rx.recv() => {
                            break;
                        }
                    }
                }
            });

            Ok(())
        }

        /// Start a simple echo server
        async fn start_echo_server(&mut self) -> Result<(), Box<dyn std::error::Error>> {
            self.start_with_handler(|ws_stream| async move {
                let (mut ws_sender, mut ws_receiver) = ws_stream.split();
                
                while let Some(msg) = ws_receiver.next().await {
                    match msg {
                        Ok(Message::Text(text)) => {
                            // Echo the message back
                            if ws_sender.send(Message::Text(text)).await.is_err() {
                                break;
                            }
                        }
                        Ok(Message::Close(_)) => break,
                        Ok(Message::Ping(data)) => {
                            if ws_sender.send(Message::Pong(data)).await.is_err() {
                                break;
                            }
                        }
                        _ => {}
                    }
                }
            }).await
        }

        /// Start a server that sends specific server messages
        async fn start_server_with_messages(
            &mut self,
            messages: Vec<ServerMessage>,
        ) -> Result<(), Box<dyn std::error::Error>> {
            self.start_with_handler(move |ws_stream| {
                let messages = messages.clone();
                async move {
                    let (mut ws_sender, mut ws_receiver) = ws_stream.split();
                    
                    // Wait for authentication message
                    if let Some(Ok(Message::Text(_auth_msg))) = ws_receiver.next().await {
                        // Send all configured messages
                        for server_msg in messages {
                            if let Ok(msg_json) = serde_json::to_string(&server_msg) {
                                if ws_sender.send(Message::Text(msg_json)).await.is_err() {
                                    break;
                                }
                            }
                        }
                    }
                    
                    // Keep connection alive briefly
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }).await
        }

        /// Start a server that closes connection immediately
        async fn start_failing_server(&mut self) -> Result<(), Box<dyn std::error::Error>> {
            self.start_with_handler(|ws_stream| async move {
                let (mut ws_sender, _) = ws_stream.split();
                // Close connection immediately
                let _ = ws_sender.close().await;
            }).await
        }

        /// Shutdown the mock server
        async fn shutdown(&mut self) {
            if let Some(shutdown_tx) = self.shutdown_tx.take() {
                let _ = shutdown_tx.send(()).await;
            }
        }
    }

    /// Helper function to create a test client
    fn create_test_client(server_url: &str) -> Result<Client, Box<dyn std::error::Error>> {
        let config = ClientConfig::new(Url::parse(server_url)?);
        Ok(Client::new(config)?)
    }

    #[tokio::test]
    async fn test_successful_connection() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_echo_server().await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        
        // Test connection
        let result = client.connect().await;
        assert!(result.is_ok(), "Connection should succeed");
        assert!(client.is_connected(), "Client should be connected");
        assert_eq!(client.connection_state, ConnectionState::Connected);

        // Clean up
        let _ = client.disconnect().await;
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_connection_with_invalid_url() {
        let mut client = create_test_client("ws://invalid-host:12345").unwrap();
        
        let result = client.connect().await;
        assert!(result.is_err(), "Connection should fail with invalid URL");
        assert!(!client.is_connected(), "Client should not be connected");
        
        match client.connection_state {
            ConnectionState::ConnectionError(_) => {}, // Expected
            _ => panic!("Expected ConnectionError state"),
        }
    }

    #[tokio::test]
    async fn test_connection_state_transitions() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_echo_server().await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        
        // Initial state should be Disconnected
        assert_eq!(client.connection_state, ConnectionState::Disconnected);
        
        // Connect
        client.connect().await.unwrap();
        assert_eq!(client.connection_state, ConnectionState::Connected);
        
        // Disconnect
        client.disconnect().await.unwrap();
        assert_eq!(client.connection_state, ConnectionState::Disconnected);

        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_double_connect_is_idempotent() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_echo_server().await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        
        // First connection
        client.connect().await.unwrap();
        assert!(client.is_connected());
        
        // Second connection should be no-op
        let result = client.connect().await;
        assert!(result.is_ok(), "Second connect should succeed (no-op)");
        assert!(client.is_connected());

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_url_scheme_conversion() {
        // Test HTTP to WS conversion
        let client = create_test_client("http://example.com").unwrap();
        assert_eq!(client.connection_state, ConnectionState::Disconnected);
        
        // Test HTTPS to WSS conversion
        let client = create_test_client("https://example.com").unwrap();
        assert_eq!(client.connection_state, ConnectionState::Disconnected);
        
        // Test direct WS URL (should not be modified)
        let client = create_test_client("ws://example.com/ws").unwrap();
        assert_eq!(client.connection_state, ConnectionState::Disconnected);
        
        // Test direct WSS URL (should not be modified)
        let client = create_test_client("wss://example.com/ws").unwrap();
        assert_eq!(client.connection_state, ConnectionState::Disconnected);
    }

    #[tokio::test]
    async fn test_invalid_url_scheme() {
        let result = create_test_client("ftp://example.com");
        // This should fail during client creation or connection
        if let Ok(mut client) = result {
            let connect_result = client.connect().await;
            assert!(connect_result.is_err(), "Connection with invalid scheme should fail");
        }
    }

    #[tokio::test]
    async fn test_server_message_handling() {
        let sender_id = Uuid::new_v4();
        let message_id = Uuid::new_v4();
        let transfer_id = Uuid::new_v4();
        
        let server_messages = vec![
            ServerMessage::MessageRelay {
                sender_id,
                recipient_id: Uuid::new_v4(),
                encrypted_payload: b"test message".to_vec(),
                metadata: HashMap::new(),
                message_type: "text".to_string(),
                message_id: Some(message_id),
            },
            ServerMessage::FileTransferInitiated {
                sender_id,
                recipient_id: Uuid::new_v4(),
                transfer_id,
                file_name: "test.txt".to_string(),
                file_size: 1024,
                file_hash: "hash123".to_string(),
                upload_url: "http://example.com/upload".to_string(),
                download_url: "http://example.com/download".to_string(),
                expires_at: std::time::SystemTime::now() + Duration::from_secs(3600),
            },
            ServerMessage::Error {
                error_code: "TEST_ERROR".to_string(),
                error_message: "Test error message".to_string(),
                request_id: None,
            },
        ];

        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_server_with_messages(server_messages).await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        // Collect events with timeout
        let mut received_events = Vec::new();
        let timeout_duration = Duration::from_secs(2);
        
        while received_events.len() < 3 {
            match timeout(timeout_duration, client.next_event()).await {
                Ok(Some(event)) => {
                    received_events.push(event);
                }
                Ok(None) => break,
                Err(_) => break, // Timeout
            }
        }

        // Verify we received the expected events
        assert!(!received_events.is_empty(), "Should receive at least one event");
        
        // Check for MessageReceived event
        let has_message_received = received_events.iter().any(|event| {
            matches!(event, ClientEvent::MessageReceived { sender_id: s, message_id: Some(m), .. } 
                     if *s == sender_id && *m == message_id)
        });
        assert!(has_message_received, "Should receive MessageReceived event");

        // Check for FileTransferInitiated event
        let has_file_transfer = received_events.iter().any(|event| {
            matches!(event, ClientEvent::FileTransferInitiated { sender_id: s, transfer_id: t, .. } 
                     if *s == sender_id && *t == transfer_id)
        });
        assert!(has_file_transfer, "Should receive FileTransferInitiated event");

        // Check for ServerError event
        let has_server_error = received_events.iter().any(|event| {
            matches!(event, ClientEvent::ServerError { error_code, .. } 
                     if error_code == "TEST_ERROR")
        });
        assert!(has_server_error, "Should receive ServerError event");

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_client_message_sending() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        
        // Track received messages
        let (msg_tx, mut msg_rx) = mpsc::channel::<String>(10);
        
        mock_server.start_with_handler(move |ws_stream| {
            let msg_tx = msg_tx.clone();
            async move {
                let (mut ws_sender, mut ws_receiver) = ws_stream.split();
                
                while let Some(msg) = ws_receiver.next().await {
                    match msg {
                        Ok(Message::Text(text)) => {
                            // Forward received message to test
                            let _ = msg_tx.send(text.clone()).await;
                            
                            // Send acknowledgment
                            let ack = serde_json::json!({
                                "type": "ConnectionAck",
                                "data": {
                                    "session_id": "test-session",
                                    "server_time": std::time::SystemTime::now()
                                }
                            });
                            if ws_sender.send(Message::Text(ack.to_string())).await.is_err() {
                                break;
                            }
                        }
                        Ok(Message::Close(_)) => break,
                        _ => {}
                    }
                }
            }
        }).await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        // Wait for authentication message
        let auth_msg = timeout(Duration::from_secs(1), msg_rx.recv()).await.unwrap().unwrap();
        
        // Verify authentication message structure
        let auth_parsed: serde_json::Value = serde_json::from_str(&auth_msg).unwrap();
        assert_eq!(auth_parsed["type"], "Authenticate");
        assert!(auth_parsed["data"]["client_id"].is_string());

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_connection_error_handling() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_failing_server().await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        // Wait for connection state change event
        let timeout_duration = Duration::from_secs(2);
        let mut connection_lost = false;
        
        while let Ok(Some(event)) = timeout(timeout_duration, client.next_event()).await {
            match event {
                ClientEvent::ConnectionStateChanged(ConnectionState::ConnectionLost) |
                ClientEvent::ConnectionStateChanged(ConnectionState::Disconnected) => {
                    connection_lost = true;
                    break;
                }
                _ => continue,
            }
        }

        assert!(connection_lost, "Should detect connection loss");

        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_ping_pong_handling() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        
        let (ping_tx, mut ping_rx) = mpsc::channel::<Vec<u8>>(10);
        
        mock_server.start_with_handler(move |ws_stream| {
            let ping_tx = ping_tx.clone();
            async move {
                let (mut ws_sender, mut ws_receiver) = ws_stream.split();
                
                // Wait for auth message
                if let Some(Ok(Message::Text(_))) = ws_receiver.next().await {
                    // Send ping
                    let ping_data = b"ping_test".to_vec();
                    if ws_sender.send(Message::Ping(ping_data.clone())).await.is_ok() {
                        // Wait for pong response
                        if let Some(Ok(Message::Pong(pong_data))) = ws_receiver.next().await {
                            let _ = ping_tx.send(pong_data).await;
                        }
                    }
                }
            }
        }).await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        // Wait for pong response
        let pong_data = timeout(Duration::from_secs(2), ping_rx.recv()).await.unwrap().unwrap();
        assert_eq!(pong_data, b"ping_test", "Pong data should match ping data");

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_malformed_server_message_handling() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        
        mock_server.start_with_handler(|ws_stream| async move {
            let (mut ws_sender, mut ws_receiver) = ws_stream.split();
            
            // Wait for auth message
            if let Some(Ok(Message::Text(_))) = ws_receiver.next().await {
                // Send malformed JSON
                let _ = ws_sender.send(Message::Text("invalid json".to_string())).await;
                
                // Send valid message after malformed one
                let valid_msg = ServerMessage::Error {
                    error_code: "AFTER_MALFORMED".to_string(),
                    error_message: "This should still work".to_string(),
                    request_id: None,
                };
                if let Ok(msg_json) = serde_json::to_string(&valid_msg) {
                    let _ = ws_sender.send(Message::Text(msg_json)).await;
                }
            }
        }).await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        let mut received_parse_error = false;
        let mut received_valid_message = false;
        let timeout_duration = Duration::from_secs(2);
        
        while let Ok(Some(event)) = timeout(timeout_duration, client.next_event()).await {
            match event {
                ClientEvent::ServerError { error_code, error_message } => {
                    if error_code == "MESSAGE_PARSE_ERROR" {
                        assert!(error_message.contains("Failed to parse server message"));
                        received_parse_error = true;
                    } else if error_code == "AFTER_MALFORMED" {
                        received_valid_message = true;
                    }
                }
                _ => continue,
            }
            
            if received_parse_error && received_valid_message {
                break;
            }
        }

        assert!(received_parse_error, "Should receive parse error for malformed JSON");
        assert!(received_valid_message, "Should still process valid messages after malformed ones");

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_binary_message_rejection() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        
        mock_server.start_with_handler(|ws_stream| async move {
            let (mut ws_sender, mut ws_receiver) = ws_stream.split();
            
            // Wait for auth message
            if let Some(Ok(Message::Text(_))) = ws_receiver.next().await {
                // Send binary message (should be rejected)
                let _ = ws_sender.send(Message::Binary(b"binary data".to_vec())).await;
            }
        }).await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();

        // Wait for binary message rejection
        let timeout_duration = Duration::from_secs(2);
        let mut received_binary_error = false;
        
        while let Ok(Some(event)) = timeout(timeout_duration, client.next_event()).await {
            match event {
                ClientEvent::ServerError { error_code, .. } => {
                    if error_code == "UNSUPPORTED_MESSAGE_TYPE" {
                        received_binary_error = true;
                        break;
                    }
                }
                _ => continue,
            }
        }

        assert!(received_binary_error, "Should reject binary messages");

        client.disconnect().await.unwrap();
        mock_server.shutdown().await;
    }

    #[tokio::test]
    async fn test_event_loop_cleanup_on_disconnect() {
        let mut mock_server = MockWebSocketServer::new().await.unwrap();
        mock_server.start_echo_server().await.unwrap();

        let mut client = create_test_client(&mock_server.ws_url()).unwrap();
        client.connect().await.unwrap();
        
        // Verify event loop is running
        assert!(client.event_loop_handle.is_some());
        
        // Disconnect
        client.disconnect().await.unwrap();
        
        // Verify cleanup
        assert!(client.event_loop_handle.is_none());
        assert!(client.event_receiver.is_none());
        assert!(client.message_sender.is_none());

        mock_server.shutdown().await;
    }
}