[package]
name = "indidus_e2ee_client"
version = "0.1.0"
edition = "2021"

[dependencies]
# Core protocol dependency
indidus_signal_protocol = { path = "../indidus_signal_protocol" }
indidus_shared = { path = "../indidus_shared" }

# Async runtime and networking
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = { version = "0.20", features = ["rustls-tls-webpki-roots"] }
tokio-stream = "0.1"
futures-util = "0.3"
sha2 = "0.10"
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }

# Serialization and data handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Cryptography
ed25519-dalek = "2.0"
aes-gcm = "0.10"
rand = "0.8"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
base64 = "0.21"
url = { version = "2.4", features = ["serde"] }

[dev-dependencies]
# Testing utilities
wiremock = "0.5"
tokio-test = "0.4"
# WebSocket mocking for connection tests
tokio-tungstenite = { version = "0.20", features = ["rustls-tls-webpki-roots"] }
futures-util = "0.3"
# Additional testing utilities
tempfile = "3.8"
serde_json = "1.0"
