//! # Basic Messaging Server Example
//!
//! This example demonstrates how to create a simple WebSocket server using the
//! Indidus E2EE server components. It shows how to:
//!
//! 1. **Initialize the routing table** - for managing client connections
//! 2. **Set up a TCP listener** - to accept incoming connections
//! 3. **Handle WebSocket upgrades** - using the reusable MessageHandler
//! 4. **Process client messages** - with full registration and relay functionality
//!
//! ## Usage
//!
//! Run this server with:
//! ```bash
//! # Using default port (8080)
//! cargo run --example basic_messaging_server
//!
//! # Using a custom port
//! cargo run --example basic_messaging_server -- --port 3000
//! ```
//!
//! Then connect clients using the basic_messaging client example:
//! ```bash
//! cargo run --example basic_messaging -- ws://127.0.0.1:8080/ws
//! ```

use async_trait::async_trait;
use indidus_e2ee_server::{
    Message, RoutingTable, WebSocketError, WebSocketStream,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio_tungstenite::{
    accept_async, tungstenite::protocol::Message as TungsteniteMessage,
    WebSocketStream as TungsteniteWebSocketStream,
};
use tracing::{error, info};
use uuid::Uuid;

/// Client-to-server message types that match what the client actually sends
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum ClientMessage {
    /// Authentication message (what the client actually sends)
    Authenticate {
        client_id: Uuid,
        credentials: Vec<u8>,
        display_name: Option<String>,
    },
    /// Send a message to another client
    SendMessage {
        recipient_id: String, // Using String instead of PeerId for simplicity
        encrypted_payload: Vec<u8>,
        metadata: HashMap<String, String>,
        message_type: String,
        client_message_id: Option<String>,
    },
    /// Pong response to server ping
    Pong {
        ping_timestamp: std::time::SystemTime,
    },
}

/// Server-to-client message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum ServerMessage {
    /// Connection acknowledgment
    ConnectionAck {
        session_id: String,
        server_time: std::time::SystemTime,
    },
    /// Message relay from another client
    MessageRelay {
        sender_id: Uuid,
        recipient_id: Uuid,
        encrypted_payload: Vec<u8>,
        metadata: HashMap<String, String>,
        message_type: String,
        message_id: Option<Uuid>,
    },
    /// Server error notification
    Error {
        error_code: String,
        error_message: String,
        request_id: Option<String>,
    },
    /// Ping message for keepalive
    Ping {
        timestamp: std::time::SystemTime,
    },
    /// Pong response to ping
    Pong {
        ping_timestamp: std::time::SystemTime,
        pong_timestamp: std::time::SystemTime,
    },
}

/// Wrapper around tokio-tungstenite WebSocketStream to implement our WebSocketStream trait
#[derive(Debug)]
struct TungsteniteWebSocketWrapper {
    stream: TungsteniteWebSocketStream<TcpStream>,
    is_active: bool,
}

impl TungsteniteWebSocketWrapper {
    fn new(stream: TungsteniteWebSocketStream<TcpStream>) -> Self {
        Self {
            stream,
            is_active: true,
        }
    }
}

#[async_trait]
impl WebSocketStream for TungsteniteWebSocketWrapper {
    async fn send(&mut self, message: Message) -> Result<(), WebSocketError> {
        if !self.is_active {
            return Err(WebSocketError::ConnectionClosed);
        }

        let tungstenite_msg = match message {
            Message::Text(text) => TungsteniteMessage::Text(text),
            Message::Binary(data) => TungsteniteMessage::Binary(data),
            Message::Close => {
                self.is_active = false;
                TungsteniteMessage::Close(None)
            }
            Message::Ping(data) => TungsteniteMessage::Ping(data),
            Message::Pong(data) => TungsteniteMessage::Pong(data),
        };

        use futures_util::SinkExt;
        self.stream
            .send(tungstenite_msg)
            .await
            .map_err(|e| WebSocketError::SendError(e.to_string()))?;

        Ok(())
    }

    async fn next(&mut self) -> Option<Result<Message, WebSocketError>> {
        if !self.is_active {
            return None;
        }

        use futures_util::StreamExt;
        match self.stream.next().await {
            Some(Ok(tungstenite_msg)) => {
                let message = match tungstenite_msg {
                    TungsteniteMessage::Text(text) => Message::Text(text),
                    TungsteniteMessage::Binary(data) => Message::Binary(data),
                    TungsteniteMessage::Close(_) => {
                        self.is_active = false;
                        Message::Close
                    }
                    TungsteniteMessage::Ping(data) => Message::Ping(data),
                    TungsteniteMessage::Pong(data) => Message::Pong(data),
                    TungsteniteMessage::Frame(_) => {
                        // Skip raw frames
                        return self.next().await;
                    }
                };
                Some(Ok(message))
            }
            Some(Err(e)) => Some(Err(WebSocketError::ReceiveError(e.to_string()))),
            None => {
                self.is_active = false;
                None
            }
        }
    }

    async fn close(&mut self) -> Result<(), WebSocketError> {
        self.is_active = false;
        self.stream
            .close(None)
            .await
            .map_err(|e| WebSocketError::SendError(e.to_string()))?;
        Ok(())
    }

    fn is_active(&self) -> bool {
        self.is_active
    }
}

/// Parse command line arguments for server configuration
fn parse_args() -> (String, u16) {
    let args: Vec<String> = env::args().collect();
    let mut host = "127.0.0.1".to_string();
    let mut port = 8080u16;

    let mut i = 1;
    while i < args.len() {
        match args[i].as_str() {
            "--host" => {
                if i + 1 < args.len() {
                    host = args[i + 1].clone();
                    i += 2;
                } else {
                    eprintln!("Error: --host requires a value");
                    std::process::exit(1);
                }
            }
            "--port" => {
                if i + 1 < args.len() {
                    port = args[i + 1].parse().unwrap_or_else(|_| {
                        eprintln!("Error: Invalid port number");
                        std::process::exit(1);
                    });
                    i += 2;
                } else {
                    eprintln!("Error: --port requires a value");
                    std::process::exit(1);
                }
            }
            _ => {
                eprintln!("Unknown argument: {}", args[i]);
                eprintln!("Usage: {} [--host HOST] [--port PORT]", args[0]);
                std::process::exit(1);
            }
        }
    }

    (host, port)
}

/// Handle a single TCP connection by upgrading it to WebSocket and processing messages
async fn handle_tcp_connection(
    tcp_stream: TcpStream,
    routing_table: Arc<RoutingTable>,
    peer_addr: SocketAddr,
) {
    info!("New TCP connection from: {}", peer_addr);

    // Upgrade the TCP connection to WebSocket
    let ws_stream = match accept_async(tcp_stream).await {
        Ok(ws_stream) => ws_stream,
        Err(e) => {
            error!("Failed to upgrade connection to WebSocket: {}", e);
            return;
        }
    };

    info!("WebSocket connection established with: {}", peer_addr);

    // Wrap the WebSocket stream to implement our trait
    let mut wrapped_stream = TungsteniteWebSocketWrapper::new(ws_stream);

    // Handle the connection with our custom message processing
    if let Err(e) = handle_client_connection(&mut wrapped_stream, routing_table).await {
        error!(
            "Error handling WebSocket connection from {}: {}",
            peer_addr, e
        );
    } else {
        info!("WebSocket connection from {} closed gracefully", peer_addr);
    }
}

/// Handle a client connection with proper authentication support
async fn handle_client_connection(
    stream: &mut TungsteniteWebSocketWrapper,
    _routing_table: Arc<RoutingTable>,
) -> Result<(), WebSocketError> {
    let mut authenticated = false;
    let mut client_id: Option<Uuid> = None;

    // Main message processing loop
    while let Some(message_result) = stream.next().await {
        match message_result {
            Ok(message) => {
                match message {
                    Message::Text(text) => {
                        // Try to parse as client message
                        match serde_json::from_str::<ClientMessage>(&text) {
                            Ok(client_msg) => {
                                match client_msg {
                                    ClientMessage::Authenticate { client_id: cid, display_name, .. } => {
                                        info!("Client authentication: {} ({})", cid, display_name.unwrap_or_else(|| "unnamed".to_string()));
                                        
                                        // Store client info
                                        client_id = Some(cid);
                                        authenticated = true;
                                        
                                        // Send connection acknowledgment
                                        let ack = ServerMessage::ConnectionAck {
                                            session_id: format!("session-{}", cid),
                                            server_time: std::time::SystemTime::now(),
                                        };
                                        
                                        let ack_json = serde_json::to_string(&ack)
                                            .map_err(|e| WebSocketError::InvalidMessage(e.to_string()))?;
                                        
                                        stream.send(Message::Text(ack_json)).await?;
                                        info!("Sent connection acknowledgment to client {}", cid);
                                    }
                                    ClientMessage::SendMessage { recipient_id, encrypted_payload, .. } => {
                                        if !authenticated {
                                            let error = ServerMessage::Error {
                                                error_code: "AUTHENTICATION_REQUIRED".to_string(),
                                                error_message: "Must authenticate before sending messages".to_string(),
                                                request_id: None,
                                            };
                                            let error_json = serde_json::to_string(&error)
                                                .map_err(|e| WebSocketError::InvalidMessage(e.to_string()))?;
                                            stream.send(Message::Text(error_json)).await?;
                                            continue;
                                        }
                                        
                                        info!("Message relay request from {:?} to {}", client_id, recipient_id);
                                        
                                        // For now, just acknowledge the message
                                        // In a real implementation, we would relay to the recipient
                                        info!("Message received: {} bytes", encrypted_payload.len());
                                    }
                                    ClientMessage::Pong { .. } => {
                                        // Handle pong response
                                        info!("Received pong from client");
                                    }
                                }
                            }
                            Err(e) => {
                                error!("Failed to parse client message: {}", e);
                                let error = ServerMessage::Error {
                                    error_code: "INVALID_MESSAGE".to_string(),
                                    error_message: format!("Failed to parse message: {}", e),
                                    request_id: None,
                                };
                                let error_json = serde_json::to_string(&error)
                                    .map_err(|e| WebSocketError::InvalidMessage(e.to_string()))?;
                                stream.send(Message::Text(error_json)).await?;
                            }
                        }
                    }
                    Message::Ping(data) => {
                        // Respond to ping with pong
                        stream.send(Message::Pong(data)).await?;
                    }
                    Message::Pong(_) => {
                        // Handle pong response
                    }
                    Message::Close => {
                        info!("Client requested connection close");
                        break;
                    }
                    Message::Binary(_) => {
                        // Handle binary messages if needed
                        info!("Received binary message (not implemented)");
                    }
                }
            }
            Err(e) => {
                error!("Error receiving message: {}", e);
                break;
            }
        }
    }

    if let Some(cid) = client_id {
        info!("Client {} disconnected", cid);
    }

    Ok(())
}

/// Main server function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for logging
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    println!("🚀 Starting Basic Messaging Server...");

    // Parse command line arguments
    let (host, port) = parse_args();
    let addr = format!("{}:{}", host, port);

    // Create the routing table for managing client connections
    let routing_table = Arc::new(RoutingTable::new());
    info!("✅ Routing table initialized");

    // Bind to the specified address
    let listener = TcpListener::bind(&addr).await?;
    println!("🌐 Server listening on: {}", addr);
    println!("📡 WebSocket endpoint: ws://{}/", addr);
    println!();
    println!("💡 Connect clients using:");
    println!("   cargo run --example basic_messaging -- ws://{}/", addr);
    println!();
    println!("🔄 Waiting for connections...");

    // Main server loop - accept connections and spawn handlers
    loop {
        match listener.accept().await {
            Ok((tcp_stream, peer_addr)) => {
                let routing_table_clone = Arc::clone(&routing_table);

                // Spawn a new task to handle this connection
                tokio::spawn(async move {
                    handle_tcp_connection(tcp_stream, routing_table_clone, peer_addr).await;
                });
            }
            Err(e) => {
                error!("Failed to accept TCP connection: {}", e);
                // Continue accepting other connections
            }
        }
    }
}
