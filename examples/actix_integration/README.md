# Actix Integration Example

This example demonstrates how to integrate the `indidus_e2ee_server` library into an existing Actix-web application, providing end-to-end encryption capabilities through WebSocket connections.

## Overview

The integration showcases the **standardized framework-specific handler pattern** for adding E2EE functionality to an existing web application. It demonstrates:

- **State Management**: Using Actix's `web::Data` for dependency injection
- **WebSocket Integration**: Real-time E2EE messaging capabilities using native Actix handlers
- **Message Processing**: Direct use of E2EE library types (ClientMessage/ServerMessage)
- **Connection Management**: Routing table integration with proper cleanup
- **Error Handling**: Framework-specific error types and responses

## Architecture

### State Management Pattern

The example uses a simplified `AppState` struct that contains the E2EE routing table:

```rust
#[derive(Clone)]
pub struct AppState {
    pub routing_table: RoutingTable,         // WebSocket routing
}
```

This state is registered with Actix using `web::Data::new(app_state)` and becomes available to all handlers through dependency injection.

### WebSocket Handler Pattern

The WebSocket handler follows the standardized framework-specific pattern:

1. **Extract State**: Use `web::Data<AppState>` to access the routing table
2. **Upgrade Connection**: Use Actix's native WebSocket upgrade mechanism
3. **Handle Messages**: Process E2EE library types directly (ClientMessage/ServerMessage)
4. **Manage Connections**: Register/unregister peers in the routing table

Example:
```rust
pub async fn websocket_handler(
    req: HttpRequest,
    stream: web::Payload,
    app_state: Data<AppState>,
) -> ActixResult<HttpResponse> {
    // Upgrade the connection to WebSocket using Actix's native upgrade
    let (response, session, msg_stream) = actix_ws::handle(&req, stream)?;
    
    // Clone the routing table for this connection
    let routing_table = app_state.routing_table.clone();
    
    // Handle the WebSocket connection using the standardized pattern
    actix_web::rt::spawn(async move {
        handle_websocket_connection(session, msg_stream, routing_table).await
    });
    
    Ok(response)
}
```

## Available Endpoints

### Health Check Endpoint
- **GET /health** - Application health check
- Returns JSON response with service status and available endpoints

### WebSocket Endpoint
- **GET /ws** - WebSocket upgrade for real-time E2EE messaging
- Handles ClientMessage types: Register, SendMessage, FileTransfer, Heartbeat
- Responds with ServerMessage types: MessageDelivered, MessageReceived, FileTransferStatus, Error

## Running the Example

### Prerequisites

1. Rust 1.70+ installed
2. All workspace dependencies available

### Build and Run

```bash
# From the project root
cargo run --example actix_integration

# Or from the example directory
cd examples/actix_integration
cargo run
```

The server will start on `http://127.0.0.1:8080`.

### Testing the Integration

#### 1. Health Check
```bash
curl http://127.0.0.1:8080/health
```

#### 2. WebSocket Connection
```javascript
// In browser console or WebSocket client
const ws = new WebSocket('ws://127.0.0.1:8080/ws');

// Register as a peer
ws.onopen = () => {
    ws.send(JSON.stringify({
        "Register": { "peer_id": "test-peer-123" }
    }));
};

// Handle server responses
ws.onmessage = (event) => {
    console.log('Received:', JSON.parse(event.data));
};

// Send a message to another peer
ws.send(JSON.stringify({
    "SendMessage": {
        "recipient_id": "other-peer",
        "payload": [72, 101, 108, 108, 111]  // "Hello" in bytes
    }
}));
```

#### 3. Using the Basic Messaging Example
```bash
# In another terminal, run the basic messaging client
cargo run --example basic_messaging_client -- --server-url ws://127.0.0.1:8080/ws
```

## Integration Patterns

### Adding to Existing Applications

To integrate E2EE capabilities into your existing Actix application:

1. **Add Dependencies**: Include `indidus_e2ee_server` in your `Cargo.toml`

2. **Create State Adapter**: Define an `AppState` struct combining your existing state with the E2EE routing table

3. **Add WebSocket Handler**: Create a WebSocket handler that uses the standardized framework-specific pattern

4. **Register Routes**: Add the WebSocket endpoint to your existing route configuration

5. **Handle Messages**: Process E2EE library types directly (ClientMessage/ServerMessage)

### Production Considerations

- **WebSocket Scaling**: For production, implement proper WebSocket connection management and load balancing
- **Authentication**: Add authentication middleware to protect WebSocket connections
- **Rate Limiting**: Implement rate limiting for WebSocket connections and message processing
- **Monitoring**: Add metrics and logging for E2EE operations
- **TLS**: Ensure all connections use HTTPS/WSS in production
- **Connection Cleanup**: Implement proper cleanup of routing table entries when connections close

## Key Benefits

1. **Framework-Specific Pattern**: Uses the standardized framework-specific handler pattern for optimal integration
2. **Type Safety**: Leverages Rust's type system for safe state management and message handling
3. **Performance**: Minimal overhead through efficient routing table and native WebSocket handling
4. **Maintainable**: Clear separation between web framework and E2EE logic
5. **Testable**: WebSocket handlers can be tested independently with mock routing tables

## Comparison with Other Frameworks

This Actix integration demonstrates the standardized framework-specific pattern with Actix-specific features:

- **State Management**: Uses `web::Data` for dependency injection
- **WebSocket Handling**: Uses `actix-ws` for native WebSocket support
- **Message Processing**: Direct use of E2EE library types (ClientMessage/ServerMessage)
- **Error Handling**: Follows Actix's error handling patterns and response builders

The core E2EE message types and routing logic remain the same across all framework integrations, showcasing the framework-agnostic design of the `indidus_e2ee_server` library while allowing each framework to use its native patterns.