//! Main server implementation for the Indidus E2EE Server
//!
//! This module defines the core `Server` struct that manages the server's state,
//! handles client connections, and coordinates message relay operations.

use crate::config::ServerConfig;
use crate::handlers::{
    file_transfer::handle_file_chunk,
    websocket::{handle_websocket, RoutingTable},
};
use crate::storage::{ChunkStorageService, StorageConfig};
use axum::extract::WebSocketUpgrade;
use axum::routing::{get, post};
use axum::Router;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Client session information stored by the server
#[derive(Debug, Clone)]
pub struct ClientSession {
    /// Unique client identifier
    pub client_id: Uuid,

    /// Client's display name (if provided)
    pub display_name: Option<String>,

    /// Timestamp of last activity
    pub last_activity: std::time::SystemTime,

    /// Whether the client is currently connected
    pub is_connected: bool,

    /// Number of stored pre-keys for this client
    pub prekey_count: u32,
}

/// Server state that needs to be shared across handlers
#[derive(Debug)]
pub struct ServerState {
    /// Server configuration
    pub config: ServerConfig,

    /// Active client sessions (keyed by client ID)
    pub client_sessions: RwLock<HashMap<Uuid, ClientSession>>,

    /// Pre-key storage (client_id -> list of pre-keys)
    pub prekey_storage: RwLock<HashMap<Uuid, Vec<Vec<u8>>>>,

    /// Message queue for offline clients (client_id -> list of messages)
    pub message_queue: RwLock<HashMap<Uuid, Vec<Vec<u8>>>>,

    /// Server startup time
    pub startup_time: std::time::SystemTime,
}

/// The main server struct for the Indidus E2EE Server
///
/// This struct represents the server instance and provides methods for
/// starting, stopping, and managing the server's lifecycle.
#[derive(Debug)]
pub struct Server {
    /// Shared server state
    state: Arc<ServerState>,

    /// Axum router for handling HTTP requests
    #[allow(dead_code)]
    router: Option<Router>,

    /// Whether the server is currently running
    is_running: bool,
}

impl ClientSession {
    /// Create a new client session
    pub fn new(client_id: Uuid, display_name: Option<String>) -> Self {
        Self {
            client_id,
            display_name,
            last_activity: std::time::SystemTime::now(),
            is_connected: true,
            prekey_count: 0,
        }
    }

    /// Update the last activity timestamp
    pub fn update_activity(&mut self) {
        self.last_activity = std::time::SystemTime::now();
    }

    /// Check if the session has been idle for too long
    pub fn is_idle(&self, max_idle_secs: u64) -> bool {
        if let Ok(duration) = self.last_activity.elapsed() {
            duration.as_secs() > max_idle_secs
        } else {
            false
        }
    }
}

impl ServerState {
    /// Create new server state with the given configuration
    pub fn new(config: ServerConfig) -> Self {
        Self {
            config,
            client_sessions: RwLock::new(HashMap::new()),
            prekey_storage: RwLock::new(HashMap::new()),
            message_queue: RwLock::new(HashMap::new()),
            startup_time: std::time::SystemTime::now(),
        }
    }

    /// Get the number of active client sessions
    pub async fn active_session_count(&self) -> usize {
        let sessions = self.client_sessions.read().await;
        sessions.values().filter(|s| s.is_connected).count()
    }

    /// Get the total number of registered clients
    pub async fn total_client_count(&self) -> usize {
        let sessions = self.client_sessions.read().await;
        sessions.len()
    }

    /// Get server uptime in seconds
    pub fn uptime_seconds(&self) -> u64 {
        self.startup_time
            .elapsed()
            .map(|d| d.as_secs())
            .unwrap_or(0)
    }

    /// Add or update a client session
    pub async fn add_client_session(&self, session: ClientSession) {
        let mut sessions = self.client_sessions.write().await;
        sessions.insert(session.client_id, session);
    }

    /// Remove a client session
    pub async fn remove_client_session(&self, client_id: &Uuid) -> Option<ClientSession> {
        let mut sessions = self.client_sessions.write().await;
        sessions.remove(client_id)
    }

    /// Get a client session by ID
    pub async fn get_client_session(&self, client_id: &Uuid) -> Option<ClientSession> {
        let sessions = self.client_sessions.read().await;
        sessions.get(client_id).cloned()
    }

    /// Update client connection status
    pub async fn set_client_connected(&self, client_id: &Uuid, connected: bool) {
        let mut sessions = self.client_sessions.write().await;
        if let Some(session) = sessions.get_mut(client_id) {
            session.is_connected = connected;
            if connected {
                session.update_activity();
            }
        }
    }

    /// Clean up idle sessions
    pub async fn cleanup_idle_sessions(&self) -> usize {
        let max_idle = self.config.max_session_idle_secs;
        let mut sessions = self.client_sessions.write().await;
        let initial_count = sessions.len();

        sessions.retain(|_, session| !session.is_idle(max_idle));

        initial_count - sessions.len()
    }
}

impl Server {
    /// Create a new server instance with the given configuration
    ///
    /// # Arguments
    /// * `config` - The server configuration
    ///
    /// # Returns
    /// A new `Server` instance ready for initialization
    pub fn new(config: ServerConfig) -> Self {
        let state = Arc::new(ServerState::new(config));

        Self {
            state,
            router: None,
            is_running: false,
        }
    }

    /// Get a reference to the server configuration
    pub fn config(&self) -> &ServerConfig {
        &self.state.config
    }

    /// Get a reference to the server state
    pub fn state(&self) -> Arc<ServerState> {
        Arc::clone(&self.state)
    }

    /// Check if the server is currently running
    pub fn is_running(&self) -> bool {
        self.is_running
    }

    /// Get the socket address the server will bind to
    pub fn socket_addr(&self) -> Result<std::net::SocketAddr, std::net::AddrParseError> {
        self.state.config.socket_addr()
    }

    /// Get server statistics
    pub async fn stats(&self) -> ServerStats {
        ServerStats {
            uptime_seconds: self.state.uptime_seconds(),
            active_sessions: self.state.active_session_count().await,
            total_clients: self.state.total_client_count().await,
            is_running: self.is_running,
        }
    }

    /// Create the Axum router with all HTTP routes configured
    pub fn create_router() -> Router {
        // Create routing table for WebSocket connections
        let routing_table = RoutingTable::new();

        // Create storage service with default configuration
        let storage_service = Arc::new(ChunkStorageService::with_default_config());

        Router::new()
            // WebSocket endpoint for real-time messaging
            .route(
                "/ws",
                get({
                    let routing_table = routing_table.clone();
                    move |ws: WebSocketUpgrade| async move {
                        ws.on_upgrade(move |socket| async move {
                            if let Err(e) = handle_websocket(socket, routing_table).await {
                                tracing::error!("WebSocket handler error: {}", e);
                            }
                        })
                    }
                }),
            )
            // HTTP endpoint for file chunk uploads
            .route("/v1/files/chunk", post(handle_file_chunk))
            // Add the storage service as shared state
            .with_state(storage_service)
    }

    /// Create the Axum router with custom storage configuration
    pub fn create_router_with_storage(storage_config: StorageConfig) -> Router {
        // Create routing table for WebSocket connections
        let routing_table = RoutingTable::new();

        // Create storage service with custom configuration
        let storage_service = Arc::new(ChunkStorageService::new(storage_config));

        Router::new()
            // WebSocket endpoint for real-time messaging
            .route(
                "/ws",
                get({
                    let routing_table = routing_table.clone();
                    move |ws: WebSocketUpgrade| async move {
                        ws.on_upgrade(move |socket| async move {
                            if let Err(e) = handle_websocket(socket, routing_table).await {
                                tracing::error!("WebSocket handler error: {}", e);
                            }
                        })
                    }
                }),
            )
            // HTTP endpoint for file chunk uploads
            .route("/v1/files/chunk", post(handle_file_chunk))
            // Add the storage service as shared state
            .with_state(storage_service)
    }
}

/// Server statistics for monitoring and health checks
#[derive(Debug, Clone)]
pub struct ServerStats {
    /// Server uptime in seconds
    pub uptime_seconds: u64,

    /// Number of currently active sessions
    pub active_sessions: usize,

    /// Total number of registered clients
    pub total_clients: usize,

    /// Whether the server is running
    pub is_running: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_server_creation() {
        let config = ServerConfig::default();
        let server = Server::new(config.clone());

        assert_eq!(server.config().host, config.host);
        assert_eq!(server.config().port, config.port);
        assert!(!server.is_running());
    }

    #[test]
    fn test_client_session_creation() {
        let client_id = Uuid::new_v4();
        let session = ClientSession::new(client_id, Some("Test Client".to_string()));

        assert_eq!(session.client_id, client_id);
        assert_eq!(session.display_name, Some("Test Client".to_string()));
        assert!(session.is_connected);
        assert_eq!(session.prekey_count, 0);
    }

    #[test]
    fn test_client_session_idle_check() {
        let mut session = ClientSession::new(Uuid::new_v4(), None);

        // Fresh session should not be idle
        assert!(!session.is_idle(60));

        // Manually set old timestamp
        session.last_activity = std::time::SystemTime::now() - std::time::Duration::from_secs(120);

        // Should be idle with 60 second threshold
        assert!(session.is_idle(60));
        assert!(!session.is_idle(180));
    }

    #[tokio::test]
    async fn test_server_state_operations() {
        let config = ServerConfig::default();
        let state = ServerState::new(config);

        let client_id = Uuid::new_v4();
        let session = ClientSession::new(client_id, Some("Test".to_string()));

        // Add session
        state.add_client_session(session.clone()).await;
        assert_eq!(state.total_client_count().await, 1);
        assert_eq!(state.active_session_count().await, 1);

        // Disconnect client
        state.set_client_connected(&client_id, false).await;
        assert_eq!(state.active_session_count().await, 0);
        assert_eq!(state.total_client_count().await, 1);

        // Remove session
        let removed = state.remove_client_session(&client_id).await;
        assert!(removed.is_some());
        assert_eq!(state.total_client_count().await, 0);
    }

    // === Additional tests to cover uncovered lines ===

    #[test]
    fn test_client_session_update_activity() {
        // Test lines 87-89 (update_activity method)
        let mut session = ClientSession::new(Uuid::new_v4(), None);
        let old_time = session.last_activity;

        // Wait a bit to ensure time difference
        std::thread::sleep(std::time::Duration::from_millis(10));

        session.update_activity();
        assert!(session.last_activity > old_time);
    }

    #[test]
    fn test_client_session_is_idle_error_case() {
        // Test lines 95-97 (error case in is_idle method)
        let mut session = ClientSession::new(Uuid::new_v4(), None);

        // Set a future time to trigger the error case
        session.last_activity = std::time::SystemTime::now() + std::time::Duration::from_secs(60);

        // Should return false when elapsed() returns an error
        assert!(!session.is_idle(30));
    }

    #[test]
    fn test_server_state_uptime_seconds() {
        // Test lines 126-131 (uptime_seconds method)
        let config = ServerConfig::default();
        let state = ServerState::new(config);

        let uptime = state.uptime_seconds();
        assert!(uptime < u64::MAX); // Should be a reasonable value

        // Wait a bit and check again
        std::thread::sleep(std::time::Duration::from_millis(10));
        let uptime2 = state.uptime_seconds();
        assert!(uptime2 >= uptime); // Should be same or greater
    }

    #[test]
    fn test_server_config_getter() {
        // Test lines 193-195 (config method)
        let config = ServerConfig::default().with_address("test.example.com".to_string(), 9999);
        let server = Server::new(config.clone());

        let server_config = server.config();
        assert_eq!(server_config.host, "test.example.com");
        assert_eq!(server_config.port, 9999);
    }

    #[test]
    fn test_server_state_getter() {
        // Test lines 198-200 (state method)
        let config = ServerConfig::default();
        let server = Server::new(config);

        let state = server.state();
        assert_eq!(state.config.host, "127.0.0.1");
        assert_eq!(state.config.port, 8080);
    }

    #[test]
    fn test_server_socket_addr() {
        // Test lines 208-210 (socket_addr method)
        let config = ServerConfig::default().with_address("*************".to_string(), 8443);
        let server = Server::new(config);

        let addr = server.socket_addr().unwrap();
        assert_eq!(addr.to_string(), "*************:8443");
    }

    #[tokio::test]
    async fn test_server_stats() {
        // Test lines 213-219 (stats method)
        let config = ServerConfig::default();
        let server = Server::new(config);

        let stats = server.stats().await;
        assert_eq!(stats.active_sessions, 0);
        assert_eq!(stats.total_clients, 0);
        assert!(!stats.is_running);
        assert!(stats.uptime_seconds < u64::MAX); // Should be a reasonable value
    }

    #[test]
    fn test_server_stats_struct() {
        // Test the ServerStats struct
        let stats = ServerStats {
            uptime_seconds: 3600,
            active_sessions: 5,
            total_clients: 10,
            is_running: true,
        };

        assert_eq!(stats.uptime_seconds, 3600);
        assert_eq!(stats.active_sessions, 5);
        assert_eq!(stats.total_clients, 10);
        assert!(stats.is_running);
    }

    #[test]
    fn test_server_is_running() {
        // Test lines 203-205 (is_running method)
        let config = ServerConfig::default();
        let server = Server::new(config);

        // Server should not be running initially
        assert!(!server.is_running());
    }
}
