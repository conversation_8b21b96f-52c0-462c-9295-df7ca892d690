use uuid::Uuid;

/// Result type for handler operations
pub type HandlerResult<T> = Result<T, HandlerError>;

#[cfg(test)]
mod tests;

/// Errors that can occur in handler operations
#[derive(Debug, thiserror::Error)]
pub enum HandlerError {
    #[error("Authentication failed: {0}")]
    AuthenticationFailed(String),
    
    #[error("Client not found: {0}")]
    ClientNotFound(Uuid),
    
    #[error("Invalid request: {0}")]
    InvalidRequest(String),
    
    #[error("Internal server error: {0}")]
    InternalError(String),
    
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    
    #[error("Serialization error: {0}")]
    SerializationError(String),
    
    #[error("Network error: {0}")]
    NetworkError(String),
}
