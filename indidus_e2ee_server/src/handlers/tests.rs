use std::collections::HashMap;

use axum::body::Body;
use axum::http::{Request, StatusCode};
use reqwest::multipart::{Form, Part};
use tempfile::TempDir;
use tokio::fs;
use tower::ServiceExt;
use uuid::Uuid;

use crate::handlers::file_transfer::FileChunkResponse;
use crate::handlers::traits::{FileTransferStatus, RelayMessageRequest, TransferStatus};
use crate::storage::StorageConfig;

use super::error::HandlerError;

#[tokio::test]
async fn test_file_chunk_endpoint_integration() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 10 * 1024 * 1024, // 10MB
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    // Test data
    let transfer_id = "test_transfer_12345";
    let chunk_index = 42u32;
    let chunk_data = b"This is test chunk data for integration testing!";

    // Create multipart form data
    let form = Form::new()
        .text("transfer_id", transfer_id)
        .text("chunk_index", chunk_index.to_string())
        .part("chunk_data", Part::bytes(chunk_data.to_vec()));

    // Send the request
    let response = send_multipart_request(&app, form).await;

    // Assert the response status
    assert_eq!(response.status(), StatusCode::OK);

    // Parse the response body
    let body_bytes = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .expect("Failed to read response body");

    let response_json: FileChunkResponse =
        serde_json::from_slice(&body_bytes).expect("Failed to parse response JSON");

    // Assert response content
    assert_eq!(response_json.transfer_id, transfer_id);
    assert_eq!(response_json.chunk_index, chunk_index);
    assert!(response_json.message.contains("stored successfully"));

    // Verify the file was actually created on disk
    let expected_file_path = temp_dir
        .path()
        .join(transfer_id)
        .join(chunk_index.to_string());

    assert!(expected_file_path.exists(), "Chunk file was not created");

    // Verify the file content
    let stored_data = fs::read(&expected_file_path)
        .await
        .expect("Failed to read stored chunk file");

    assert_eq!(
        stored_data, chunk_data,
        "Stored data does not match original"
    );
}

#[tokio::test]
async fn test_file_chunk_endpoint_validation_errors() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 100, // Very small for testing
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    // Test 1: Missing transfer_id
    let response = send_multipart_request_with_data(
        &app,
        None, // missing transfer_id
        Some("0"),
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 2: Missing chunk_index
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        None, // missing chunk_index
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 3: Missing chunk_data
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        Some("0"),
        None, // missing chunk_data
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 4: Invalid chunk_index (not a number)
    let response = send_multipart_request_with_data(
        &app,
        Some("test_transfer"),
        Some("not_a_number"),
        Some(b"test"),
    )
    .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // Test 5: Chunk too large
    let large_data = vec![0u8; 200]; // Larger than max_chunk_size (100)
    let response =
        send_multipart_request_with_data(&app, Some("test_transfer"), Some("0"), Some(&large_data))
            .await;
    assert_eq!(response.status(), StatusCode::PAYLOAD_TOO_LARGE);

    // Test 6: Invalid transfer_id (path traversal attempt)
    let response =
        send_multipart_request_with_data(&app, Some("../evil_path"), Some("0"), Some(b"test"))
            .await;
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);
}

#[tokio::test]
async fn test_file_chunk_endpoint_duplicate_chunk() {
    // Create a temporary directory for testing
    let temp_dir = TempDir::new().expect("Failed to create temp directory");
    let storage_config = StorageConfig {
        base_dir: temp_dir.path().to_path_buf(),
        max_chunk_size: 10 * 1024 * 1024,
        max_chunks_per_transfer: 1000,
    };

    // Create the router with test storage configuration
    let app = crate::server::Server::create_router_with_storage(storage_config);

    let transfer_id = "test_duplicate";
    let chunk_index = 0u32;
    let chunk_data = b"duplicate test data";

    // Send the first request (should succeed)
    let response = send_multipart_request_with_data(
        &app,
        Some(transfer_id),
        Some(&chunk_index.to_string()),
        Some(chunk_data),
    )
    .await;
    assert_eq!(response.status(), StatusCode::OK);

    // Send the same request again (should fail with conflict)
    let response = send_multipart_request_with_data(
        &app,
        Some(transfer_id),
        Some(&chunk_index.to_string()),
        Some(chunk_data),
    )
    .await;
    assert_eq!(response.status(), StatusCode::CONFLICT);
}

// Helper function to send multipart requests in tests
async fn send_multipart_request(app: &axum::Router, _form: Form) -> axum::response::Response {
    send_multipart_request_with_data(
        app,
        Some("test_transfer_12345"),
        Some("42"),
        Some(b"This is test chunk data for integration testing!"),
    )
    .await
}

// More flexible helper function for different test scenarios
async fn send_multipart_request_with_data(
    app: &axum::Router,
    transfer_id: Option<&str>,
    chunk_index: Option<&str>,
    chunk_data: Option<&[u8]>,
) -> axum::response::Response {
    let boundary = "----formdata-test-boundary";
    let mut body = String::new();

    // Add transfer_id field if provided
    if let Some(id) = transfer_id {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"transfer_id\"\r\n\r\n");
        body.push_str(&format!("{}\r\n", id));
    }

    // Add chunk_index field if provided
    if let Some(index) = chunk_index {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"chunk_index\"\r\n\r\n");
        body.push_str(&format!("{}\r\n", index));
    }

    // Add chunk_data field if provided
    if let Some(data) = chunk_data {
        body.push_str(&format!("--{}\r\n", boundary));
        body.push_str("Content-Disposition: form-data; name=\"chunk_data\"\r\n\r\n");
        // Don't add extra newline for binary data
        body.push_str(&String::from_utf8_lossy(data));
        body.push_str("\r\n");
    }

    body.push_str(&format!("--{}--\r\n", boundary));

    let content_type = format!("multipart/form-data; boundary={}", boundary);

    let request = Request::builder()
        .method("POST")
        .uri("/v1/files/chunk")
        .header("content-type", content_type)
        .body(Body::from(body))
        .expect("Failed to build test request");

    app.clone()
        .oneshot(request)
        .await
        .expect("Failed to execute request")
}

#[test]
fn test_relay_message_request_serialization() {
    let request = RelayMessageRequest {
        sender_id: Uuid::new_v4(),
        recipient_id: Uuid::new_v4(),
        encrypted_payload: vec![1, 2, 3, 4],
        metadata: HashMap::new(),
        message_type: "text".to_string(),
    };

    let json = serde_json::to_string(&request).unwrap();
    let deserialized: RelayMessageRequest = serde_json::from_str(&json).unwrap();

    assert_eq!(request.sender_id, deserialized.sender_id);
    assert_eq!(request.recipient_id, deserialized.recipient_id);
    assert_eq!(request.encrypted_payload, deserialized.encrypted_payload);
}

#[test]
fn test_file_transfer_status() {
    let status = FileTransferStatus {
        transfer_id: Uuid::new_v4(),
        status: TransferStatus::Uploading,
        bytes_uploaded: 1024,
        bytes_downloaded: 0,
        total_size: 2048,
        created_at: std::time::SystemTime::now(),
        expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(3600),
    };

    assert_eq!(status.bytes_uploaded, 1024);
    assert_eq!(status.total_size, 2048);
    assert!(matches!(status.status, TransferStatus::Uploading));
}

#[test]
fn test_handler_error_display() {
    let error = HandlerError::ClientNotFound(Uuid::new_v4());
    let error_string = format!("{}", error);
    assert!(error_string.contains("Client not found"));

    let auth_error = HandlerError::AuthenticationFailed("Invalid token".to_string());
    let auth_string = format!("{}", auth_error);
    assert!(auth_string.contains("Authentication failed"));
}

#[cfg(test)]
mod websocket_tests {
    use crate::handlers::websocket::{ClientMessage, ServerMessage, RoutingTable};
    use indidus_shared::validation::PeerId;
    use tokio::sync::mpsc;
    use serde_json;

    #[tokio::test]
    async fn test_routing_table_new() {
        let routing_table = RoutingTable::new();
        assert_eq!(routing_table.connection_count().await, 0);
        assert!(routing_table.connected_peers().await.is_empty());
    }

    #[tokio::test]
    async fn test_routing_table_default() {
        let routing_table = RoutingTable::default();
        assert_eq!(routing_table.connection_count().await, 0);
    }

    #[tokio::test]
    async fn test_routing_table_insert_and_contains() {
        let routing_table = RoutingTable::new();
        let peer_id = PeerId::try_from("test_peer_123").unwrap();
        let (sender, _) = mpsc::unbounded_channel();

        // Initially not present
        assert!(!routing_table.contains_peer(&peer_id).await);
        assert_eq!(routing_table.connection_count().await, 0);

        // Insert peer
        routing_table.insert(peer_id.clone(), sender).await;

        // Now present
        assert!(routing_table.contains_peer(&peer_id).await);
        assert_eq!(routing_table.connection_count().await, 1);
        
        let connected_peers = routing_table.connected_peers().await;
        assert_eq!(connected_peers.len(), 1);
        assert!(connected_peers.contains(&peer_id.to_string()));
    }

    #[tokio::test]
    async fn test_routing_table_remove() {
        let routing_table = RoutingTable::new();
        let peer_id = PeerId::try_from("test_peer_456").unwrap();
        let (sender, _) = mpsc::unbounded_channel();

        // Insert peer
        routing_table.insert(peer_id.clone(), sender).await;
        assert!(routing_table.contains_peer(&peer_id).await);

        // Remove peer
        let removed_sender = routing_table.remove(&peer_id).await;
        assert!(removed_sender.is_some());
        assert!(!routing_table.contains_peer(&peer_id).await);
        assert_eq!(routing_table.connection_count().await, 0);

        // Try to remove non-existent peer
        let non_existent = routing_table.remove(&peer_id).await;
        assert!(non_existent.is_none());
    }

    #[tokio::test]
    async fn test_routing_table_send_to_peer_success() {
        let routing_table = RoutingTable::new();
        let peer_id = PeerId::try_from("test_peer_789").unwrap();
        let (sender, mut receiver) = mpsc::unbounded_channel();

        // Insert peer
        routing_table.insert(peer_id.clone(), sender).await;

        // Send message
        let message = ServerMessage::Pong;
        let result = routing_table.send_to_peer(&peer_id, message.clone()).await;
        assert!(result);

        // Verify message was received
        let received = receiver.recv().await.unwrap();
        match received {
            ServerMessage::Pong => {},
            _ => panic!("Expected Pong message"),
        }
    }

    #[tokio::test]
    async fn test_routing_table_send_to_peer_not_found() {
        let routing_table = RoutingTable::new();
        let peer_id = PeerId::try_from("nonexistent_peer").unwrap();

        // Try to send to non-existent peer
        let message = ServerMessage::Pong;
        let result = routing_table.send_to_peer(&peer_id, message).await;
        assert!(!result);
    }

    #[tokio::test]
    async fn test_routing_table_send_to_peer_closed_channel() {
        let routing_table = RoutingTable::new();
        let peer_id = PeerId::try_from("test_peer_closed").unwrap();
        let (sender, receiver) = mpsc::unbounded_channel();

        // Insert peer
        routing_table.insert(peer_id.clone(), sender).await;

        // Drop receiver to close channel
        drop(receiver);

        // Try to send message - should fail
        let message = ServerMessage::Pong;
        let result = routing_table.send_to_peer(&peer_id, message).await;
        assert!(!result);
    }

    #[tokio::test]
    async fn test_routing_table_multiple_peers() {
        let routing_table = RoutingTable::new();
        let peer1 = PeerId::try_from("peer_one").unwrap();
        let peer2 = PeerId::try_from("peer_two").unwrap();
        let peer3 = PeerId::try_from("peer_three").unwrap();

        let (sender1, _) = mpsc::unbounded_channel();
        let (sender2, _) = mpsc::unbounded_channel();
        let (sender3, _) = mpsc::unbounded_channel();

        // Insert multiple peers
        routing_table.insert(peer1.clone(), sender1).await;
        routing_table.insert(peer2.clone(), sender2).await;
        routing_table.insert(peer3.clone(), sender3).await;

        assert_eq!(routing_table.connection_count().await, 3);
        
        let connected_peers = routing_table.connected_peers().await;
        assert_eq!(connected_peers.len(), 3);
        assert!(connected_peers.contains(&peer1.to_string()));
        assert!(connected_peers.contains(&peer2.to_string()));
        assert!(connected_peers.contains(&peer3.to_string()));

        // Remove one peer
        routing_table.remove(&peer2).await;
        assert_eq!(routing_table.connection_count().await, 2);
        assert!(!routing_table.contains_peer(&peer2).await);
        assert!(routing_table.contains_peer(&peer1).await);
        assert!(routing_table.contains_peer(&peer3).await);
    }

    #[test]
    fn test_client_message_serialization() {
        let messages = vec![
            ClientMessage::Register {
                peer_id: PeerId::try_from("test_peer").unwrap(),
            },
            ClientMessage::Relay {
                recipient_id: PeerId::try_from("recipient").unwrap(),
                payload: vec![1, 2, 3, 4],
            },
            ClientMessage::Ping,
            ClientMessage::Disconnect,
        ];

        for message in messages {
            let json = serde_json::to_string(&message).unwrap();
            let deserialized: ClientMessage = serde_json::from_str(&json).unwrap();
            
            // Verify the message type matches
            match (&message, &deserialized) {
                (ClientMessage::Register { peer_id: p1 }, ClientMessage::Register { peer_id: p2 }) => {
                    assert_eq!(p1, p2);
                },
                (ClientMessage::Relay { recipient_id: r1, payload: pl1 }, 
                 ClientMessage::Relay { recipient_id: r2, payload: pl2 }) => {
                    assert_eq!(r1, r2);
                    assert_eq!(pl1, pl2);
                },
                (ClientMessage::Ping, ClientMessage::Ping) => {},
                (ClientMessage::Disconnect, ClientMessage::Disconnect) => {},
                _ => panic!("Message types don't match after serialization"),
            }
        }
    }

    #[test]
    fn test_server_message_serialization() {
        let messages = vec![
            ServerMessage::RegisterSuccess {
                peer_id: PeerId::try_from("test_peer").unwrap(),
            },
            ServerMessage::MessageRelayed {
                sender_id: PeerId::try_from("sender").unwrap(),
                payload: vec![5, 6, 7, 8],
            },
            ServerMessage::Pong,
            ServerMessage::Error {
                reason: "Test error".to_string(),
            },
            ServerMessage::PeerDisconnected {
                peer_id: PeerId::try_from("disconnected_peer").unwrap(),
            },
        ];

        for message in messages {
            let json = serde_json::to_string(&message).unwrap();
            let deserialized: ServerMessage = serde_json::from_str(&json).unwrap();
            
            // Verify the message type matches
            match (&message, &deserialized) {
                (ServerMessage::RegisterSuccess { peer_id: p1 }, 
                 ServerMessage::RegisterSuccess { peer_id: p2 }) => {
                    assert_eq!(p1, p2);
                },
                (ServerMessage::MessageRelayed { sender_id: s1, payload: pl1 }, 
                 ServerMessage::MessageRelayed { sender_id: s2, payload: pl2 }) => {
                    assert_eq!(s1, s2);
                    assert_eq!(pl1, pl2);
                },
                (ServerMessage::Pong, ServerMessage::Pong) => {},
                (ServerMessage::Error { reason: r1 }, ServerMessage::Error { reason: r2 }) => {
                    assert_eq!(r1, r2);
                },
                (ServerMessage::PeerDisconnected { peer_id: p1 }, 
                 ServerMessage::PeerDisconnected { peer_id: p2 }) => {
                    assert_eq!(p1, p2);
                },
                _ => panic!("Message types don't match after serialization"),
            }
        }
    }

    #[test]
    fn test_client_message_invalid_json() {
        let invalid_json = r#"{"type": "InvalidType"}"#;
        let result: Result<ClientMessage, _> = serde_json::from_str(invalid_json);
        assert!(result.is_err());
    }

    #[test]
    fn test_client_message_missing_fields() {
        let incomplete_json = r#"{"type": "Register"}"#;
        let result: Result<ClientMessage, _> = serde_json::from_str(incomplete_json);
        assert!(result.is_err());
    }

    #[test]
    fn test_server_message_invalid_json() {
        let invalid_json = r#"{"type": "InvalidServerType"}"#;
        let result: Result<ServerMessage, _> = serde_json::from_str(invalid_json);
        assert!(result.is_err());
    }

    #[test]
    fn test_client_message_register_deserialization() {
        // Test successful deserialization with valid peer ID
        let valid_json = r#"{"type": "Register", "peer_id": "valid_peer_123"}"#;
        let result: Result<ClientMessage, _> = serde_json::from_str(valid_json);
        assert!(result.is_ok());
        
        if let Ok(ClientMessage::Register { peer_id }) = result {
            assert_eq!(peer_id.as_str(), "valid_peer_123");
        } else {
            panic!("Expected Register message");
        }
        
        // Note: PeerId validation happens at the type level during construction,
        // not during serde deserialization. Invalid peer IDs would be caught
        // when the PeerId is used in business logic that validates it.
    }

    #[test]
    fn test_client_message_relay_empty_payload() {
        let relay_json = r#"{"type": "Relay", "recipient_id": "valid_peer", "payload": []}"#;
        let result: Result<ClientMessage, _> = serde_json::from_str(relay_json);
        assert!(result.is_ok());
        
        if let Ok(ClientMessage::Relay { payload, .. }) = result {
            assert!(payload.is_empty());
        }
    }

    #[test]
    fn test_client_message_relay_large_payload() {
        let large_payload = vec![42u8; 1000];
        let message = ClientMessage::Relay {
            recipient_id: PeerId::try_from("test_recipient").unwrap(),
            payload: large_payload.clone(),
        };
        
        let json = serde_json::to_string(&message).unwrap();
        let deserialized: ClientMessage = serde_json::from_str(&json).unwrap();
        
        if let ClientMessage::Relay { payload, .. } = deserialized {
            assert_eq!(payload, large_payload);
        } else {
            panic!("Expected Relay message");
        }
    }

    #[test]
    fn test_server_message_error_with_special_characters() {
        let error_message = ServerMessage::Error {
            reason: "Error with special chars: áéíóú ñ 中文 🚀".to_string(),
        };
        
        let json = serde_json::to_string(&error_message).unwrap();
        let deserialized: ServerMessage = serde_json::from_str(&json).unwrap();
        
        if let ServerMessage::Error { reason } = deserialized {
            assert_eq!(reason, "Error with special chars: áéíóú ñ 中文 🚀");
        } else {
            panic!("Expected Error message");
        }
    }

    #[test]
    fn test_message_type_field_consistency() {
        // Test that the "type" field is correctly set for all message variants
        let register_msg = ClientMessage::Register {
            peer_id: PeerId::try_from("test").unwrap(),
        };
        let register_json = serde_json::to_string(&register_msg).unwrap();
        assert!(register_json.contains(r#""type":"Register""#));

        let ping_msg = ClientMessage::Ping;
        let ping_json = serde_json::to_string(&ping_msg).unwrap();
        assert!(ping_json.contains(r#""type":"Ping""#));

        let pong_msg = ServerMessage::Pong;
        let pong_json = serde_json::to_string(&pong_msg).unwrap();
        assert!(pong_json.contains(r#""type":"Pong""#));
    }
}
