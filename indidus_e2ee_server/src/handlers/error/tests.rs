//! Unit tests for HandlerError variants and conversions
//!
//! This module provides comprehensive testing for all handler error variants
//! to ensure proper error handling throughout the server handler operations.

use super::*;
use uuid::Uuid;

#[cfg(test)]
mod tests {
    use super::*;

    /// Test HandlerError variant construction
    #[test]
    fn test_handler_error_variants() {
        // Test AuthenticationFailed
        let auth_error = HandlerError::AuthenticationFailed("Invalid token".to_string());
        match auth_error {
            HandlerError::AuthenticationFailed(msg) => {
                assert_eq!(msg, "Invalid token");
            }
            _ => panic!("Expected AuthenticationFailed error"),
        }

        // Test ClientNotFound
        let client_id = Uuid::new_v4();
        let client_error = HandlerError::ClientNotFound(client_id);
        match client_error {
            HandlerError::ClientNotFound(id) => {
                assert_eq!(id, client_id);
            }
            _ => panic!("Expected ClientNotFound error"),
        }

        // Test InvalidRequest
        let request_error = HandlerError::InvalidRequest("Missing required field".to_string());
        match request_error {
            HandlerError::InvalidRequest(msg) => {
                assert_eq!(msg, "Missing required field");
            }
            _ => panic!("Expected InvalidRequest error"),
        }

        // Test InternalError
        let internal_error = HandlerError::InternalError("Database connection failed".to_string());
        match internal_error {
            HandlerError::InternalError(msg) => {
                assert_eq!(msg, "Database connection failed");
            }
            _ => panic!("Expected InternalError error"),
        }

        // Test RateLimitExceeded
        let rate_limit_error = HandlerError::RateLimitExceeded;
        match rate_limit_error {
            HandlerError::RateLimitExceeded => {}, // Success
            _ => panic!("Expected RateLimitExceeded error"),
        }

        // Test SerializationError
        let serialization_error = HandlerError::SerializationError("JSON encoding failed".to_string());
        match serialization_error {
            HandlerError::SerializationError(msg) => {
                assert_eq!(msg, "JSON encoding failed");
            }
            _ => panic!("Expected SerializationError error"),
        }

        // Test NetworkError
        let network_error = HandlerError::NetworkError("Connection timeout".to_string());
        match network_error {
            HandlerError::NetworkError(msg) => {
                assert_eq!(msg, "Connection timeout");
            }
            _ => panic!("Expected NetworkError error"),
        }
    }

    /// Test error message formatting
    #[test]
    fn test_error_message_formatting() {
        // Test AuthenticationFailed message
        let auth_error = HandlerError::AuthenticationFailed("Token expired".to_string());
        let error_msg = format!("{}", auth_error);
        assert_eq!(error_msg, "Authentication failed: Token expired");

        // Test ClientNotFound message
        let client_id = Uuid::new_v4();
        let client_error = HandlerError::ClientNotFound(client_id);
        let error_msg = format!("{}", client_error);
        assert_eq!(error_msg, format!("Client not found: {}", client_id));

        // Test InvalidRequest message
        let request_error = HandlerError::InvalidRequest("Invalid JSON".to_string());
        let error_msg = format!("{}", request_error);
        assert_eq!(error_msg, "Invalid request: Invalid JSON");

        // Test InternalError message
        let internal_error = HandlerError::InternalError("Panic occurred".to_string());
        let error_msg = format!("{}", internal_error);
        assert_eq!(error_msg, "Internal server error: Panic occurred");

        // Test RateLimitExceeded message
        let rate_limit_error = HandlerError::RateLimitExceeded;
        let error_msg = format!("{}", rate_limit_error);
        assert_eq!(error_msg, "Rate limit exceeded");

        // Test SerializationError message
        let serialization_error = HandlerError::SerializationError("Buffer overflow".to_string());
        let error_msg = format!("{}", serialization_error);
        assert_eq!(error_msg, "Serialization error: Buffer overflow");

        // Test NetworkError message
        let network_error = HandlerError::NetworkError("DNS resolution failed".to_string());
        let error_msg = format!("{}", network_error);
        assert_eq!(error_msg, "Network error: DNS resolution failed");
    }

    /// Test error debug formatting
    #[test]
    fn test_error_debug_formatting() {
        let errors = vec![
            HandlerError::AuthenticationFailed("test".to_string()),
            HandlerError::ClientNotFound(Uuid::new_v4()),
            HandlerError::InvalidRequest("test".to_string()),
            HandlerError::InternalError("test".to_string()),
            HandlerError::RateLimitExceeded,
            HandlerError::SerializationError("test".to_string()),
            HandlerError::NetworkError("test".to_string()),
        ];

        for error in errors {
            let debug_str = format!("{:?}", error);
            assert!(!debug_str.is_empty());
            // Debug output should contain some indication of the error type
            assert!(debug_str.len() > 10); // Basic sanity check
        }
    }

    /// Test HandlerResult type alias
    #[test]
    fn test_handler_result_type_alias() {
        // Test successful result
        let success: HandlerResult<String> = Ok("success".to_string());
        assert_eq!(success.unwrap(), "success");

        // Test error result
        let failure: HandlerResult<String> = Err(HandlerError::RateLimitExceeded);
        assert!(failure.is_err());
        match failure.unwrap_err() {
            HandlerError::RateLimitExceeded => {}, // Success
            _ => panic!("Expected RateLimitExceeded error"),
        }
    }

    /// Test error categorization by type
    #[test]
    fn test_error_categorization() {
        // Authentication/Authorization errors
        let auth_errors = vec![
            HandlerError::AuthenticationFailed("test".to_string()),
        ];

        for error in auth_errors {
            let error_str = format!("{}", error);
            assert!(error_str.contains("Authentication"));
        }

        // Client management errors
        let client_errors = vec![
            HandlerError::ClientNotFound(Uuid::new_v4()),
        ];

        for error in client_errors {
            let error_str = format!("{}", error);
            assert!(error_str.contains("Client"));
        }

        // Request validation errors
        let request_errors = vec![
            HandlerError::InvalidRequest("test".to_string()),
        ];

        for error in request_errors {
            let error_str = format!("{}", error);
            assert!(error_str.contains("request"));
        }

        // System errors
        let system_errors = vec![
            HandlerError::InternalError("test".to_string()),
            HandlerError::RateLimitExceeded,
            HandlerError::SerializationError("test".to_string()),
            HandlerError::NetworkError("test".to_string()),
        ];

        for error in system_errors {
            let error_str = format!("{}", error);
            assert!(
                error_str.contains("Internal") ||
                error_str.contains("Rate limit") ||
                error_str.contains("Serialization") ||
                error_str.contains("Network")
            );
        }
    }

    /// Test that errors implement required traits
    #[test]
    fn test_error_traits() {
        let error = HandlerError::InternalError("test".to_string());
        
        // Test Debug trait
        let _debug = format!("{:?}", error);
        
        // Test Display trait (via Error trait)
        let _display = format!("{}", error);
        
        // Test that it implements std::error::Error
        use std::error::Error;
        let _error_trait: &dyn Error = &error;
    }

    /// Test error construction patterns
    #[test]
    fn test_error_construction_patterns() {
        // Test that all variants can be constructed
        let client_id = Uuid::new_v4();
        
        let errors = vec![
            HandlerError::AuthenticationFailed("Invalid credentials".to_string()),
            HandlerError::ClientNotFound(client_id),
            HandlerError::InvalidRequest("Malformed JSON".to_string()),
            HandlerError::InternalError("Database error".to_string()),
            HandlerError::RateLimitExceeded,
            HandlerError::SerializationError("Encoding failed".to_string()),
            HandlerError::NetworkError("Timeout".to_string()),
        ];

        // Ensure all errors are valid and can be formatted
        for error in errors {
            let _display = format!("{}", error);
            let _debug = format!("{:?}", error);
            
            // Test that they can be used in Results
            let result: HandlerResult<()> = Err(error);
            assert!(result.is_err());
        }
    }

    /// Test error message content for actionability
    #[test]
    fn test_error_message_actionability() {
        // Test that error messages provide useful information
        let auth_error = HandlerError::AuthenticationFailed("JWT token expired".to_string());
        let msg = format!("{}", auth_error);
        assert!(msg.contains("Authentication failed"));
        assert!(msg.contains("JWT token expired"));

        let client_error = HandlerError::ClientNotFound(Uuid::new_v4());
        let msg = format!("{}", client_error);
        assert!(msg.contains("Client not found"));

        let request_error = HandlerError::InvalidRequest("Missing 'type' field".to_string());
        let msg = format!("{}", request_error);
        assert!(msg.contains("Invalid request"));
        assert!(msg.contains("Missing 'type' field"));

        let internal_error = HandlerError::InternalError("Database connection pool exhausted".to_string());
        let msg = format!("{}", internal_error);
        assert!(msg.contains("Internal server error"));
        assert!(msg.contains("Database connection pool exhausted"));

        let rate_error = HandlerError::RateLimitExceeded;
        let msg = format!("{}", rate_error);
        assert_eq!(msg, "Rate limit exceeded");

        let serialization_error = HandlerError::SerializationError("Cannot serialize NaN value".to_string());
        let msg = format!("{}", serialization_error);
        assert!(msg.contains("Serialization error"));
        assert!(msg.contains("Cannot serialize NaN value"));

        let network_error = HandlerError::NetworkError("Connection refused".to_string());
        let msg = format!("{}", network_error);
        assert!(msg.contains("Network error"));
        assert!(msg.contains("Connection refused"));
    }

    /// Test error equality and comparison (if implemented)
    #[test]
    fn test_error_comparison() {
        // Test that same error types with same data are equal (if PartialEq is implemented)
        let error1 = HandlerError::RateLimitExceeded;
        let error2 = HandlerError::RateLimitExceeded;
        
        // Since HandlerError doesn't derive PartialEq, we test via formatting
        assert_eq!(format!("{}", error1), format!("{}", error2));
        assert_eq!(format!("{:?}", error1), format!("{:?}", error2));

        // Test different errors produce different output
        let auth_error = HandlerError::AuthenticationFailed("test".to_string());
        let rate_error = HandlerError::RateLimitExceeded;
        
        assert_ne!(format!("{}", auth_error), format!("{}", rate_error));
    }

    /// Test error usage in realistic scenarios
    #[test]
    fn test_realistic_error_scenarios() {
        // Simulate authentication failure
        fn authenticate_user(token: &str) -> HandlerResult<String> {
            if token.is_empty() {
                Err(HandlerError::AuthenticationFailed("Empty token provided".to_string()))
            } else if token == "expired" {
                Err(HandlerError::AuthenticationFailed("Token has expired".to_string()))
            } else {
                Ok("user123".to_string())
            }
        }

        // Test authentication scenarios
        assert!(authenticate_user("").is_err());
        assert!(authenticate_user("expired").is_err());
        assert!(authenticate_user("valid").is_ok());

        // Simulate client lookup
        fn find_client(id: Uuid) -> HandlerResult<String> {
            let known_client = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
            if id == known_client {
                Ok("client_data".to_string())
            } else {
                Err(HandlerError::ClientNotFound(id))
            }
        }

        let known_id = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
        let unknown_id = Uuid::new_v4();

        assert!(find_client(known_id).is_ok());
        assert!(find_client(unknown_id).is_err());

        // Simulate request validation
        fn validate_request(data: &str) -> HandlerResult<()> {
            if data.is_empty() {
                Err(HandlerError::InvalidRequest("Request body is empty".to_string()))
            } else if !data.contains("type") {
                Err(HandlerError::InvalidRequest("Missing required 'type' field".to_string()))
            } else {
                Ok(())
            }
        }

        assert!(validate_request("").is_err());
        assert!(validate_request("{}").is_err());
        assert!(validate_request(r#"{"type": "message"}"#).is_ok());
    }
}